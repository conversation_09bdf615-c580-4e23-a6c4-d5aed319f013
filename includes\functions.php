<?php
/**
 * Common Functions
 * Employee Leave Management System
 */

/**
 * Sanitize input data
 */
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email format
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Calculate working days between two dates (excluding weekends)
 */
function calculateWorkingDays($startDate, $endDate) {
    $start = new DateTime($startDate);
    $end = new DateTime($endDate);
    $end->modify('+1 day'); // Include end date
    
    $interval = new DateInterval('P1D');
    $period = new DatePeriod($start, $interval, $end);
    
    $workingDays = 0;
    foreach ($period as $date) {
        $dayOfWeek = $date->format('N'); // 1 (Monday) to 7 (Sunday)
        if ($dayOfWeek < 6) { // Monday to Friday
            $workingDays++;
        }
    }
    
    return $workingDays;
}

/**
 * Format date for display
 */
function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * Format datetime for display
 */
function formatDateTime($datetime, $format = DISPLAY_DATETIME_FORMAT) {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}

/**
 * Get current month and year
 */
function getCurrentMonth() {
    return date('n');
}

function getCurrentYear() {
    return date('Y');
}

/**
 * Calculate leave balance for a user
 */
function calculateLeaveBalance($userId, $month = null, $year = null) {
    global $database;
    
    if ($month === null) $month = getCurrentMonth();
    if ($year === null) $year = getCurrentYear();
    
    // Get user hire date
    $user = $database->fetch("SELECT hire_date FROM users WHERE id = ?", [$userId]);
    if (!$user) return 0;
    
    $hireDate = new DateTime($user['hire_date']);
    $currentDate = new DateTime("$year-$month-01");
    
    // If hire date is after current month, no balance
    if ($hireDate > $currentDate) return 0;
    
    // Calculate total earned days up to current month
    $totalEarned = 0;
    $startYear = $hireDate->format('Y');
    $startMonth = $hireDate->format('n');
    
    for ($y = $startYear; $y <= $year; $y++) {
        $monthStart = ($y == $startYear) ? $startMonth : 1;
        $monthEnd = ($y == $year) ? $month : 12;
        
        for ($m = $monthStart; $m <= $monthEnd; $m++) {
            $totalEarned += MONTHLY_LEAVE_DAYS;
        }
    }
    
    // Calculate total used days
    $totalUsed = $database->fetch(
        "SELECT COALESCE(SUM(days_requested), 0) as total_used 
         FROM leaves 
         WHERE user_id = ? AND status = 'approved' 
         AND YEAR(from_date) <= ? 
         AND (YEAR(from_date) < ? OR MONTH(from_date) <= ?)",
        [$userId, $year, $year, $month]
    )['total_used'];
    
    return $totalEarned - $totalUsed;
}

/**
 * Update leave balances for all users for current month
 */
function updateMonthlyLeaveBalances() {
    global $database;
    
    $month = getCurrentMonth();
    $year = getCurrentYear();
    
    // Get all active employees
    $employees = $database->fetchAll(
        "SELECT id, hire_date FROM users WHERE role = 'employee' AND is_active = 1"
    );
    
    foreach ($employees as $employee) {
        $hireDate = new DateTime($employee['hire_date']);
        $currentDate = new DateTime("$year-$month-01");
        
        // Skip if employee was hired after current month
        if ($hireDate > $currentDate) continue;
        
        // Check if balance record exists
        $existing = $database->fetch(
            "SELECT id FROM leave_balances WHERE user_id = ? AND month = ? AND year = ?",
            [$employee['id'], $month, $year]
        );
        
        if (!$existing) {
            // Create new balance record
            $database->execute(
                "INSERT INTO leave_balances (user_id, month, year, days_earned, total_balance) 
                 VALUES (?, ?, ?, ?, ?)",
                [$employee['id'], $month, $year, MONTHLY_LEAVE_DAYS, MONTHLY_LEAVE_DAYS]
            );
        }
    }
}

/**
 * Get departments list
 */
function getDepartments() {
    global $database;
    return $database->fetchAll("SELECT * FROM departments ORDER BY name");
}

/**
 * Get department name by ID
 */
function getDepartmentName($departmentId) {
    global $database;
    $dept = $database->fetch("SELECT name FROM departments WHERE id = ?", [$departmentId]);
    return $dept ? $dept['name'] : 'Unknown';
}

/**
 * Send notification (placeholder for email functionality)
 */
function sendNotification($userId, $subject, $message) {
    // Placeholder for email notification
    // In a real implementation, you would integrate with an email service
    error_log("Notification to user $userId: $subject - $message");
    return true;
}

/**
 * Log activity
 */
function logActivity($userId, $action, $details = '') {
    global $database;
    
    $database->execute(
        "INSERT INTO activity_logs (user_id, action, details, ip_address, created_at) 
         VALUES (?, ?, ?, ?, NOW())",
        [$userId, $action, $details, $_SERVER['REMOTE_ADDR'] ?? '']
    );
}

/**
 * Validate date format
 */
function isValidDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * Check if date is in the future
 */
function isFutureDate($date) {
    return strtotime($date) > time();
}

/**
 * Check if end date is after start date
 */
function isValidDateRange($startDate, $endDate) {
    return strtotime($endDate) >= strtotime($startDate);
}
?>
