<?php
require_once '../config/config.php';
requireAuth();

// Ensure user is employee
if (!isEmployee()) {
    header('Location: ../admin/dashboard.php');
    exit();
}

$user_id = getCurrentUserId();
$leave_id = (int)($_GET['id'] ?? 0);

if ($leave_id <= 0) {
    $_SESSION['error_message'] = 'Invalid leave request ID.';
    header('Location: leave_history.php');
    exit();
}

// Get leave details - ensure it belongs to current user
$leave = $database->fetch(
    "SELECT l.*, admin.name as approved_by_name
     FROM leaves l 
     LEFT JOIN users admin ON l.approved_by = admin.id
     WHERE l.id = ? AND l.user_id = ?",
    [$leave_id, $user_id]
);

if (!$leave) {
    $_SESSION['error_message'] = 'Leave request not found.';
    header('Location: leave_history.php');
    exit();
}

$current_user = getCurrentUser();

$page_title = 'Leave Request Details';
include '../includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-alt me-2"></i>
                Leave Request Details
            </h1>
            <a href="leave_history.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to History
            </a>
        </div>
        
        <!-- Leave Request Card -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Request #<?php echo $leave['id']; ?>
                    </h5>
                    <span class="badge status-<?php echo $leave['status']; ?> fs-6">
                        <?php echo ucfirst($leave['status']); ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-user me-2"></i>Employee Information
                        </h6>
                        <div class="ps-3">
                            <div class="mb-2">
                                <strong>Name:</strong> <?php echo htmlspecialchars($current_user['name']); ?>
                            </div>
                            <div class="mb-2">
                                <strong>Department:</strong> <?php echo htmlspecialchars($current_user['department_name'] ?? 'N/A'); ?>
                            </div>
                            <div class="mb-2">
                                <strong>Email:</strong> <?php echo htmlspecialchars($current_user['email']); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-calendar me-2"></i>Leave Period
                        </h6>
                        <div class="ps-3">
                            <div class="mb-2">
                                <strong>From:</strong> <?php echo formatDate($leave['from_date']); ?>
                            </div>
                            <div class="mb-2">
                                <strong>To:</strong> <?php echo formatDate($leave['to_date']); ?>
                            </div>
                            <div class="mb-2">
                                <strong>Duration:</strong> 
                                <span class="badge bg-info"><?php echo $leave['days_requested']; ?> working days</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12 mb-4">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-comment me-2"></i>Reason for Leave
                        </h6>
                        <div class="ps-3">
                            <div class="bg-light p-3 rounded">
                                <?php echo nl2br(htmlspecialchars($leave['reason'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($leave['substitute_name']) || !empty($leave['substitute_address'])): ?>
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-user-friends me-2"></i>Substitute Information
                        </h6>
                        <div class="ps-3">
                            <?php if (!empty($leave['substitute_name'])): ?>
                                <div class="mb-2">
                                    <strong>Name:</strong> <?php echo htmlspecialchars($leave['substitute_name']); ?>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($leave['substitute_address'])): ?>
                                <div class="mb-2">
                                    <strong>Contact/Address:</strong> <?php echo htmlspecialchars($leave['substitute_address']); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-clock me-2"></i>Request Timeline
                        </h6>
                        <div class="ps-3">
                            <div class="mb-2">
                                <strong>Submitted:</strong> <?php echo formatDateTime($leave['created_at']); ?>
                            </div>
                            <?php if ($leave['status'] !== 'pending' && !empty($leave['approved_at'])): ?>
                                <div class="mb-2">
                                    <strong>Processed:</strong> <?php echo formatDateTime($leave['approved_at']); ?>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($leave['approved_by_name'])): ?>
                                <div class="mb-2">
                                    <strong>Processed by:</strong> <?php echo htmlspecialchars($leave['approved_by_name']); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-info-circle me-2"></i>Status Information
                        </h6>
                        <div class="ps-3">
                            <div class="mb-2">
                                <strong>Current Status:</strong> 
                                <span class="badge status-<?php echo $leave['status']; ?> ms-2">
                                    <?php echo ucfirst($leave['status']); ?>
                                </span>
                            </div>
                            <?php if ($leave['status'] === 'pending'): ?>
                                <small class="text-muted">
                                    <i class="fas fa-hourglass-half me-1"></i>
                                    Your request is awaiting admin approval.
                                </small>
                            <?php elseif ($leave['status'] === 'approved'): ?>
                                <small class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Your leave request has been approved.
                                </small>
                            <?php elseif ($leave['status'] === 'rejected'): ?>
                                <small class="text-danger">
                                    <i class="fas fa-times-circle me-1"></i>
                                    Your leave request has been rejected.
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($leave['admin_comments'])): ?>
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-comment-dots me-2"></i>Admin Comments
                        </h6>
                        <div class="ps-3">
                            <div class="alert alert-<?php echo $leave['status'] === 'approved' ? 'success' : 'warning'; ?> border-0">
                                <?php echo nl2br(htmlspecialchars($leave['admin_comments'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-footer bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Request ID: #<?php echo $leave['id']; ?>
                    </small>
                    <div>
                        <?php if ($leave['status'] === 'pending'): ?>
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>Awaiting Approval
                            </span>
                        <?php elseif ($leave['status'] === 'approved'): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Approved
                            </span>
                        <?php else: ?>
                            <span class="badge bg-danger">
                                <i class="fas fa-times me-1"></i>Rejected
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="d-flex justify-content-between mt-4">
            <a href="leave_history.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to History
            </a>
            
            <div>
                <button onclick="window.print()" class="btn btn-outline-primary me-2">
                    <i class="fas fa-print me-2"></i>Print
                </button>
                
                <?php if ($leave['status'] === 'pending'): ?>
                    <a href="apply_leave.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Apply for Another Leave
                    </a>
                <?php else: ?>
                    <a href="apply_leave.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Apply for New Leave
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .navbar, footer, .d-flex.justify-content-between.mt-4 {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: transparent !important;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
