<?php
/**
 * Leave Calculation Functions
 * Employee Leave Management System
 */

/**
 * Update monthly leave balances for all active employees
 * This should be run monthly via cron job
 */
function updateAllEmployeeBalances($month = null, $year = null) {
    global $database;
    
    if ($month === null) $month = date('n');
    if ($year === null) $year = date('Y');
    
    // Get all active employees
    $employees = $database->fetchAll(
        "SELECT id, hire_date FROM users WHERE role = 'employee' AND is_active = 1"
    );
    
    $updated_count = 0;
    
    foreach ($employees as $employee) {
        if (updateEmployeeMonthlyBalance($employee['id'], $month, $year)) {
            $updated_count++;
        }
    }
    
    return $updated_count;
}

/**
 * Update monthly balance for a specific employee
 */
function updateEmployeeMonthlyBalance($userId, $month, $year) {
    global $database;
    
    // Get employee hire date
    $user = $database->fetch("SELECT hire_date FROM users WHERE id = ?", [$userId]);
    if (!$user) return false;
    
    $hireDate = new DateTime($user['hire_date']);
    $targetDate = new DateTime("$year-$month-01");
    
    // Skip if employee was hired after this month
    if ($hireDate > $targetDate) return false;
    
    // Check if balance record already exists
    $existing = $database->fetch(
        "SELECT id, days_carried_forward FROM leave_balances 
         WHERE user_id = ? AND month = ? AND year = ?",
        [$userId, $month, $year]
    );
    
    // Calculate carried forward balance from previous month
    $carriedForward = 0;
    if ($month == 1) {
        // January - carry forward from December of previous year
        $prevBalance = $database->fetch(
            "SELECT total_balance FROM leave_balances 
             WHERE user_id = ? AND month = 12 AND year = ?",
            [$userId, $year - 1]
        );
        $carriedForward = $prevBalance ? $prevBalance['total_balance'] : 0;
    } else {
        // Other months - carry forward from previous month
        $prevBalance = $database->fetch(
            "SELECT total_balance FROM leave_balances 
             WHERE user_id = ? AND month = ? AND year = ?",
            [$userId, $month - 1, $year]
        );
        $carriedForward = $prevBalance ? $prevBalance['total_balance'] : 0;
    }
    
    // Calculate days used in this month
    $daysUsed = $database->fetch(
        "SELECT COALESCE(SUM(days_requested), 0) as total_used 
         FROM leaves 
         WHERE user_id = ? AND status = 'approved' 
         AND YEAR(from_date) = ? AND MONTH(from_date) = ?",
        [$userId, $year, $month]
    )['total_used'];
    
    $daysEarned = MONTHLY_LEAVE_DAYS;
    $totalBalance = $carriedForward + $daysEarned - $daysUsed;
    
    if ($existing) {
        // Update existing record
        $database->execute(
            "UPDATE leave_balances 
             SET days_earned = ?, days_used = ?, days_carried_forward = ?, 
                 total_balance = ?, updated_at = NOW()
             WHERE id = ?",
            [$daysEarned, $daysUsed, $carriedForward, $totalBalance, $existing['id']]
        );
    } else {
        // Create new record
        $database->execute(
            "INSERT INTO leave_balances 
             (user_id, month, year, days_earned, days_used, days_carried_forward, total_balance) 
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            [$userId, $month, $year, $daysEarned, $daysUsed, $carriedForward, $totalBalance]
        );
    }
    
    return true;
}

/**
 * Calculate total leave balance for an employee up to a specific date
 */
function calculateTotalLeaveBalance($userId, $upToDate = null) {
    global $database;
    
    if ($upToDate === null) $upToDate = date('Y-m-d');
    
    $targetYear = date('Y', strtotime($upToDate));
    $targetMonth = date('n', strtotime($upToDate));
    
    // Get user hire date
    $user = $database->fetch("SELECT hire_date FROM users WHERE id = ?", [$userId]);
    if (!$user) return 0;
    
    $hireDate = new DateTime($user['hire_date']);
    $currentDate = new DateTime($upToDate);
    
    // If hire date is after target date, no balance
    if ($hireDate > $currentDate) return 0;
    
    // Calculate total earned days up to target date
    $totalEarned = 0;
    $startYear = $hireDate->format('Y');
    $startMonth = $hireDate->format('n');
    
    for ($year = $startYear; $year <= $targetYear; $year++) {
        $monthStart = ($year == $startYear) ? $startMonth : 1;
        $monthEnd = ($year == $targetYear) ? $targetMonth : 12;
        
        for ($month = $monthStart; $month <= $monthEnd; $month++) {
            $totalEarned += MONTHLY_LEAVE_DAYS;
        }
    }
    
    // Calculate total used days up to target date
    $totalUsed = $database->fetch(
        "SELECT COALESCE(SUM(days_requested), 0) as total_used 
         FROM leaves 
         WHERE user_id = ? AND status = 'approved' 
         AND from_date <= ?",
        [$userId, $upToDate]
    )['total_used'];
    
    return $totalEarned - $totalUsed;
}

/**
 * Calculate end-of-service leave reward
 */
function calculateEndOfServiceReward($userId, $endDate = null) {
    global $database;
    
    if ($endDate === null) $endDate = date('Y-m-d');
    
    // Get user details
    $user = $database->fetch(
        "SELECT hire_date, name FROM users WHERE id = ?", 
        [$userId]
    );
    
    if (!$user) return null;
    
    $hireDate = new DateTime($user['hire_date']);
    $endServiceDate = new DateTime($endDate);
    
    // Calculate service period
    $servicePeriod = $hireDate->diff($endServiceDate);
    $serviceYears = $servicePeriod->y;
    $serviceMonths = $servicePeriod->m;
    $serviceDays = $servicePeriod->d;
    
    // Calculate total leave balance at end of service
    $totalBalance = calculateTotalLeaveBalance($userId, $endDate);
    
    // Calculate monetary value (assuming 1 day = 1/30 of monthly salary)
    // This would need to be customized based on actual salary data
    $estimatedDailyRate = 0; // Placeholder - would need salary integration
    $monetaryValue = $totalBalance * $estimatedDailyRate;
    
    return [
        'employee_name' => $user['name'],
        'hire_date' => $user['hire_date'],
        'end_date' => $endDate,
        'service_years' => $serviceYears,
        'service_months' => $serviceMonths,
        'service_days' => $serviceDays,
        'total_leave_balance' => $totalBalance,
        'monetary_value' => $monetaryValue,
        'calculation_date' => date('Y-m-d H:i:s')
    ];
}

/**
 * Get leave statistics for an employee
 */
function getEmployeeLeaveStatistics($userId, $year = null) {
    global $database;
    
    if ($year === null) $year = date('Y');
    
    $stats = [];
    
    // Total earned this year
    $stats['total_earned'] = $database->fetch(
        "SELECT COALESCE(SUM(days_earned), 0) as total 
         FROM leave_balances 
         WHERE user_id = ? AND year = ?",
        [$userId, $year]
    )['total'];
    
    // Total used this year
    $stats['total_used'] = $database->fetch(
        "SELECT COALESCE(SUM(days_requested), 0) as total 
         FROM leaves 
         WHERE user_id = ? AND status = 'approved' 
         AND YEAR(from_date) = ?",
        [$userId, $year]
    )['total'];
    
    // Current balance
    $stats['current_balance'] = calculateLeaveBalance($userId);
    
    // Pending requests
    $stats['pending_requests'] = $database->fetch(
        "SELECT COUNT(*) as count 
         FROM leaves 
         WHERE user_id = ? AND status = 'pending'",
        [$userId]
    )['count'];
    
    // Approved requests this year
    $stats['approved_requests'] = $database->fetch(
        "SELECT COUNT(*) as count 
         FROM leaves 
         WHERE user_id = ? AND status = 'approved' 
         AND YEAR(from_date) = ?",
        [$userId, $year]
    )['count'];
    
    // Rejected requests this year
    $stats['rejected_requests'] = $database->fetch(
        "SELECT COUNT(*) as count 
         FROM leaves 
         WHERE user_id = ? AND status = 'rejected' 
         AND YEAR(from_date) = ?",
        [$userId, $year]
    )['count'];
    
    // Average leave per request
    if ($stats['approved_requests'] > 0) {
        $stats['average_leave_per_request'] = $stats['total_used'] / $stats['approved_requests'];
    } else {
        $stats['average_leave_per_request'] = 0;
    }
    
    // Usage percentage
    if ($stats['total_earned'] > 0) {
        $stats['usage_percentage'] = ($stats['total_used'] / $stats['total_earned']) * 100;
    } else {
        $stats['usage_percentage'] = 0;
    }
    
    return $stats;
}

/**
 * Get department leave statistics
 */
function getDepartmentLeaveStatistics($departmentId, $year = null) {
    global $database;
    
    if ($year === null) $year = date('Y');
    
    $stats = [];
    
    // Total employees in department
    $stats['total_employees'] = $database->fetch(
        "SELECT COUNT(*) as count 
         FROM users 
         WHERE department_id = ? AND role = 'employee' AND is_active = 1",
        [$departmentId]
    )['count'];
    
    // Total leave requests this year
    $stats['total_requests'] = $database->fetch(
        "SELECT COUNT(*) as count 
         FROM leaves l 
         JOIN users u ON l.user_id = u.id 
         WHERE u.department_id = ? AND YEAR(l.from_date) = ?",
        [$departmentId, $year]
    )['count'];
    
    // Pending requests
    $stats['pending_requests'] = $database->fetch(
        "SELECT COUNT(*) as count 
         FROM leaves l 
         JOIN users u ON l.user_id = u.id 
         WHERE u.department_id = ? AND l.status = 'pending'",
        [$departmentId]
    )['count'];
    
    // Approved requests this year
    $stats['approved_requests'] = $database->fetch(
        "SELECT COUNT(*) as count 
         FROM leaves l 
         JOIN users u ON l.user_id = u.id 
         WHERE u.department_id = ? AND l.status = 'approved' 
         AND YEAR(l.from_date) = ?",
        [$departmentId, $year]
    )['count'];
    
    // Total days used this year
    $stats['total_days_used'] = $database->fetch(
        "SELECT COALESCE(SUM(l.days_requested), 0) as total 
         FROM leaves l 
         JOIN users u ON l.user_id = u.id 
         WHERE u.department_id = ? AND l.status = 'approved' 
         AND YEAR(l.from_date) = ?",
        [$departmentId, $year]
    )['total'];
    
    // Average days per employee
    if ($stats['total_employees'] > 0) {
        $stats['average_days_per_employee'] = $stats['total_days_used'] / $stats['total_employees'];
    } else {
        $stats['average_days_per_employee'] = 0;
    }
    
    return $stats;
}

/**
 * Validate leave request against business rules
 */
function validateLeaveRequest($userId, $fromDate, $toDate, $excludeLeaveId = null) {
    global $database;
    
    $errors = [];
    
    // Check if dates are valid
    if (!isValidDate($fromDate) || !isValidDate($toDate)) {
        $errors[] = 'Invalid date format';
        return $errors;
    }
    
    // Check if end date is after start date
    if (!isValidDateRange($fromDate, $toDate)) {
        $errors[] = 'End date must be after or equal to start date';
    }
    
    // Check if start date is not in the past
    if (strtotime($fromDate) < strtotime(date('Y-m-d'))) {
        $errors[] = 'Leave start date cannot be in the past';
    }
    
    // Calculate working days
    $daysRequested = calculateWorkingDays($fromDate, $toDate);
    
    // Check against maximum consecutive days
    if ($daysRequested > MAX_CONSECUTIVE_DAYS) {
        $errors[] = "Maximum consecutive leave days allowed is " . MAX_CONSECUTIVE_DAYS . " days";
    }
    
    // Check against available balance
    $currentBalance = calculateLeaveBalance($userId);
    if ($daysRequested > $currentBalance) {
        $errors[] = "Insufficient leave balance. You have {$currentBalance} days available, but requested {$daysRequested} days";
    }
    
    // Check for overlapping requests
    $overlapQuery = "SELECT id FROM leaves 
                     WHERE user_id = ? 
                     AND status IN ('pending', 'approved')
                     AND (
                         (from_date <= ? AND to_date >= ?) OR
                         (from_date <= ? AND to_date >= ?) OR
                         (from_date >= ? AND to_date <= ?)
                     )";
    
    $overlapParams = [
        $userId,
        $fromDate, $fromDate,
        $toDate, $toDate,
        $fromDate, $toDate
    ];
    
    if ($excludeLeaveId) {
        $overlapQuery .= " AND id != ?";
        $overlapParams[] = $excludeLeaveId;
    }
    
    $overlapping = $database->fetch($overlapQuery, $overlapParams);
    
    if ($overlapping) {
        $errors[] = 'You already have a leave request for overlapping dates';
    }
    
    return $errors;
}
?>
