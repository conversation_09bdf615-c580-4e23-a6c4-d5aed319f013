<?php
require_once '../config/config.php';
requireAdmin();

// Get dashboard statistics
$stats = [];

// Total employees
$totalEmployeesResult = $database->fetch(
    "SELECT COUNT(*) as count FROM users WHERE role = 'employee' AND is_active = 1"
);
$stats['total_employees'] = $totalEmployeesResult ? $totalEmployeesResult['count'] : 0;

// Total departments
$totalDepartmentsResult = $database->fetch(
    "SELECT COUNT(*) as count FROM departments"
);
$stats['total_departments'] = $totalDepartmentsResult ? $totalDepartmentsResult['count'] : 0;

// Pending leave requests
$pendingRequestsResult = $database->fetch(
    "SELECT COUNT(*) as count FROM leaves WHERE status = 'pending'"
);
$stats['pending_requests'] = $pendingRequestsResult ? $pendingRequestsResult['count'] : 0;

// This month's approved leaves
$monthlyApprovedResult = $database->fetch(
    "SELECT COUNT(*) as count FROM leaves
     WHERE status = 'approved'
     AND MONTH(from_date) = MONTH(CURRENT_DATE())
     AND YEAR(from_date) = YEAR(CURRENT_DATE())"
);
$stats['monthly_approved'] = $monthlyApprovedResult ? $monthlyApprovedResult['count'] : 0;

// Recent leave requests
$recent_requests = $database->fetchAll(
    "SELECT l.*, u.name as employee_name, d.name as department_name
     FROM leaves l
     JOIN users u ON l.user_id = u.id
     LEFT JOIN departments d ON u.department_id = d.id
     ORDER BY l.created_at DESC
     LIMIT 5"
);

// Department statistics
$department_stats = $database->fetchAll(
    "SELECT d.name, 
            COUNT(u.id) as employee_count,
            COUNT(l.id) as total_requests,
            SUM(CASE WHEN l.status = 'pending' THEN 1 ELSE 0 END) as pending_requests
     FROM departments d
     LEFT JOIN users u ON d.id = u.department_id AND u.role = 'employee' AND u.is_active = 1
     LEFT JOIN leaves l ON u.id = l.user_id
     GROUP BY d.id, d.name
     ORDER BY d.name"
);

// Monthly leave trends (last 6 months)
$monthly_trends = $database->fetchAll(
    "SELECT 
        DATE_FORMAT(from_date, '%Y-%m') as month,
        COUNT(*) as total_requests,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests
     FROM leaves 
     WHERE from_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)
     GROUP BY DATE_FORMAT(from_date, '%Y-%m')
     ORDER BY month"
);

$page_title = 'Admin Dashboard';
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>
                Admin Dashboard
            </h1>
            <div class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                <?php echo date('l, F j, Y'); ?>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card border-0 h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-users fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Total Employees</div>
                        <div class="display-6 fw-bold text-primary"><?php echo $stats['total_employees']; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card border-0 h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-building fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Departments</div>
                        <div class="display-6 fw-bold text-success"><?php echo $stats['total_departments']; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card border-0 h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-clock fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Pending Requests</div>
                        <div class="display-6 fw-bold text-warning"><?php echo $stats['pending_requests']; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card border-0 h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-check-circle fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">This Month Approved</div>
                        <div class="display-6 fw-bold text-info"><?php echo $stats['monthly_approved']; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Recent Leave Requests -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Recent Leave Requests
                    </h5>
                    <a href="leaves.php" class="btn btn-sm btn-outline-primary">
                        View All <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recent_requests)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent leave requests</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Employee</th>
                                    <th>Department</th>
                                    <th>Dates</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_requests as $request): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-semibold"><?php echo htmlspecialchars($request['employee_name']); ?></div>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo htmlspecialchars($request['department_name'] ?? 'N/A'); ?></small>
                                        </td>
                                        <td>
                                            <small>
                                                <?php echo formatDate($request['from_date']); ?> - 
                                                <?php echo formatDate($request['to_date']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge status-<?php echo $request['status']; ?>">
                                                <?php echo ucfirst($request['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="leave_details.php?id=<?php echo $request['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Department Statistics -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    Department Overview
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($department_stats)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-building fa-2x text-muted mb-3"></i>
                        <p class="text-muted">No departments found</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($department_stats as $dept): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="fw-semibold"><?php echo htmlspecialchars($dept['name']); ?></div>
                                <small class="text-muted">
                                    <?php echo $dept['employee_count']; ?> employees
                                </small>
                            </div>
                            <div class="text-end">
                                <div class="fw-semibold"><?php echo $dept['total_requests']; ?></div>
                                <small class="text-muted">requests</small>
                                <?php if ($dept['pending_requests'] > 0): ?>
                                    <span class="badge bg-warning ms-1"><?php echo $dept['pending_requests']; ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Trends Chart -->
<?php if (!empty($monthly_trends)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Leave Requests Trend (Last 6 Months)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="trendsChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    const trendsData = <?php echo json_encode($monthly_trends); ?>;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: trendsData.map(item => {
                const date = new Date(item.month + '-01');
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            }),
            datasets: [{
                label: 'Total Requests',
                data: trendsData.map(item => item.total_requests),
                borderColor: '#0d6efd',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                tension: 0.4
            }, {
                label: 'Approved Requests',
                data: trendsData.map(item => item.approved_requests),
                borderColor: '#198754',
                backgroundColor: 'rgba(25, 135, 84, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
});
</script>
<?php endif; ?>

<?php include '../includes/footer.php'; ?>
