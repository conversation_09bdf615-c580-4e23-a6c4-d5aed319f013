<?php
/**
 * Application Configuration
 * Employee Leave Management System
 * Southern Technical University Presidency
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Application settings
define('APP_NAME', 'Employee Leave Management System');
define('APP_VERSION', '1.0.0');
define('ORGANIZATION', 'Southern Technical University Presidency');

// Base URL configuration
define('BASE_URL', 'http://localhost/ark/');
define('ASSETS_URL', BASE_URL . 'assets/');

// File paths
define('ROOT_PATH', dirname(__DIR__) . '/');
define('INCLUDES_PATH', ROOT_PATH . 'includes/');
define('VIEWS_PATH', ROOT_PATH . 'views/');
define('ASSETS_PATH', ROOT_PATH . 'assets/');

// Leave system settings
define('MONTHLY_LEAVE_DAYS', 3);
define('MAX_CONSECUTIVE_DAYS', 30);

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('CSRF_TOKEN_NAME', 'csrf_token');

// Date and time settings
date_default_timezone_set('Asia/Baghdad');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// Include required files
require_once ROOT_PATH . 'config/database.php';
require_once ROOT_PATH . 'includes/functions.php';
require_once ROOT_PATH . 'includes/auth.php';
require_once ROOT_PATH . 'includes/leave_calculations.php';
require_once ROOT_PATH . 'includes/security.php';

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set default charset
ini_set('default_charset', 'UTF-8');

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// CSRF token generation
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

/**
 * Auto-redirect based on user role
 */
function redirectBasedOnRole() {
    if (isLoggedIn()) {
        if (isAdmin()) {
            if (basename($_SERVER['PHP_SELF']) !== 'admin_dashboard.php') {
                header('Location: ' . BASE_URL . 'admin/dashboard.php');
                exit();
            }
        } else {
            if (basename($_SERVER['PHP_SELF']) !== 'employee_dashboard.php') {
                header('Location: ' . BASE_URL . 'employee/dashboard.php');
                exit();
            }
        }
    }
}

/**
 * Check if current page requires authentication
 */
function requireAuth() {
    $public_pages = ['login.php', 'register.php', 'index.php'];
    $current_page = basename($_SERVER['PHP_SELF']);
    
    if (!in_array($current_page, $public_pages) && !isLoggedIn()) {
        header('Location: ' . BASE_URL . 'login.php');
        exit();
    }
}

/**
 * Check if current page requires admin access
 */
function requireAdmin() {
    requireAuth();
    if (!isAdmin()) {
        header('Location: ' . BASE_URL . 'employee/dashboard.php');
        exit();
    }
}
?>
