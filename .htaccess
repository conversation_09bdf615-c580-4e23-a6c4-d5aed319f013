# # Employee Leave Management System - Apache Configuration

# # Enable URL Rewriting
# RewriteEngine On

# # Security Headers
# <IfModule mod_headers.c>
#     # Prevent MIME type sniffing
#     Header always set X-Content-Type-Options nosniff
    
#     # Prevent clickjacking
#     Header always set X-Frame-Options DENY
    
#     # Enable XSS protection
#     Header always set X-XSS-Protection "1; mode=block"
    
#     # Referrer Policy
#     Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
#     # Remove server signature
#     Header unset Server
#     Header unset X-Powered-By
# </IfModule>

# # Hide sensitive files
# <Files ~ "^\.">
#     Order allow,deny
#     Deny from all
# </Files>

# # Protect configuration files
# <FilesMatch "\.(ini|conf|config|log|sql)$">
#     Order allow,deny
#     Deny from all
# </FilesMatch>

# # Protect PHP files in includes directory
# <Directory "includes">
#     <Files "*.php">
#         Order allow,deny
#         Deny from all
#     </Files>
# </Directory>

# # Protect database files
# <Directory "database">
#     Order allow,deny
#     Deny from all
# </Directory>

# # Enable compression
# <IfModule mod_deflate.c>
#     AddOutputFilterByType DEFLATE text/plain
#     AddOutputFilterByType DEFLATE text/html
#     AddOutputFilterByType DEFLATE text/xml
#     AddOutputFilterByType DEFLATE text/css
#     AddOutputFilterByType DEFLATE application/xml
#     AddOutputFilterByType DEFLATE application/xhtml+xml
#     AddOutputFilterByType DEFLATE application/rss+xml
#     AddOutputFilterByType DEFLATE application/javascript
#     AddOutputFilterByType DEFLATE application/x-javascript
# </IfModule>

# # Browser caching
# <IfModule mod_expires.c>
#     ExpiresActive on
    
#     # CSS and JavaScript
#     ExpiresByType text/css "access plus 1 month"
#     ExpiresByType application/javascript "access plus 1 month"
#     ExpiresByType application/x-javascript "access plus 1 month"
    
#     # Images
#     ExpiresByType image/png "access plus 1 month"
#     ExpiresByType image/jpg "access plus 1 month"
#     ExpiresByType image/jpeg "access plus 1 month"
#     ExpiresByType image/gif "access plus 1 month"
#     ExpiresByType image/ico "access plus 1 month"
#     ExpiresByType image/icon "access plus 1 month"
#     ExpiresByType image/x-icon "access plus 1 month"
    
#     # Fonts
#     ExpiresByType font/woff "access plus 1 month"
#     ExpiresByType font/woff2 "access plus 1 month"
#     ExpiresByType application/font-woff "access plus 1 month"
#     ExpiresByType application/font-woff2 "access plus 1 month"
# </IfModule>

# # Prevent access to backup files
# <FilesMatch "\.(bak|backup|old|tmp|temp)$">
#     Order allow,deny
#     Deny from all
# </FilesMatch>

# # Custom error pages (optional)
# ErrorDocument 404 /404.html
# ErrorDocument 403 /403.html
# ErrorDocument 500 /500.html

# # PHP settings
# <IfModule mod_php7.c>
#     # Hide PHP version
#     php_flag expose_php off
    
#     # Session security
#     php_value session.cookie_httponly 1
#     php_value session.cookie_secure 1
#     php_value session.use_strict_mode 1
    
#     # File upload limits
#     php_value upload_max_filesize 10M
#     php_value post_max_size 10M
#     php_value max_execution_time 30
#     php_value max_input_time 30
#     php_value memory_limit 128M
# </IfModule>

# # Prevent directory browsing
# Options -Indexes

# # Follow symbolic links
# Options +FollowSymLinks

# # Default charset
# AddDefaultCharset UTF-8

# # Force HTTPS (uncomment if using SSL)
# # RewriteCond %{HTTPS} off
# # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# # Pretty URLs (optional - for future enhancement)
# # RewriteCond %{REQUEST_FILENAME} !-f
# # RewriteCond %{REQUEST_FILENAME} !-d
# # RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L,QSA]
# # RewriteRule ^employee/([^/]+)/?$ employee/$1.php [L,QSA]
