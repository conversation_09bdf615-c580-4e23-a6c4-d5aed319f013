<?php
require_once '../config/config.php';
requireAdmin();

// Handle leave approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $_SESSION['error_message'] = 'Invalid security token.';
    } else {
        $leave_id = (int)($_POST['leave_id'] ?? 0);
        $action = $_POST['action'];
        $admin_comments = sanitize($_POST['admin_comments'] ?? '');
        
        if ($leave_id > 0 && in_array($action, ['approve', 'reject'])) {
            $status = ($action === 'approve') ? 'approved' : 'rejected';
            
            try {
                $database->execute(
                    "UPDATE leaves SET status = ?, admin_comments = ?, approved_by = ?, approved_at = NOW() 
                     WHERE id = ? AND status = 'pending'",
                    [$status, $admin_comments, getCurrentUserId(), $leave_id]
                );
                
                // Get leave details for notification
                $leave = $database->fetch(
                    "SELECT l.*, u.name as employee_name, u.email 
                     FROM leaves l 
                     JOIN users u ON l.user_id = u.id 
                     WHERE l.id = ?",
                    [$leave_id]
                );
                
                if ($leave) {
                    // Update leave balance if approved
                    if ($status === 'approved') {
                        $month = date('n', strtotime($leave['from_date']));
                        $year = date('Y', strtotime($leave['from_date']));
                        
                        // Update the leave balance for the month
                        $database->execute(
                            "UPDATE leave_balances 
                             SET days_used = days_used + ?, 
                                 total_balance = days_earned + days_carried_forward - (days_used + ?)
                             WHERE user_id = ? AND month = ? AND year = ?",
                            [$leave['days_requested'], $leave['days_requested'], $leave['user_id'], $month, $year]
                        );
                    }
                    
                    // Log activity
                    logActivity(getCurrentUserId(), 'leave_' . $action, "Leave request #{$leave_id} {$status}");
                    
                    $_SESSION['success_message'] = "Leave request has been {$status} successfully.";
                } else {
                    $_SESSION['error_message'] = 'Leave request not found or already processed.';
                }
            } catch (Exception $e) {
                $_SESSION['error_message'] = 'Error processing leave request: ' . $e->getMessage();
            }
        } else {
            $_SESSION['error_message'] = 'Invalid request parameters.';
        }
    }
    
    header('Location: leaves.php');
    exit();
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$department_filter = $_GET['department'] ?? 'all';
$search = sanitize($_GET['search'] ?? '');

// Build query conditions
$conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $conditions[] = "l.status = ?";
    $params[] = $status_filter;
}

if ($department_filter !== 'all') {
    $conditions[] = "u.department_id = ?";
    $params[] = $department_filter;
}

if (!empty($search)) {
    $conditions[] = "(u.name LIKE ? OR l.reason LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

$where_clause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

// Get leave requests with pagination
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

$total_query = "SELECT COUNT(*) as count 
                FROM leaves l 
                JOIN users u ON l.user_id = u.id 
                LEFT JOIN departments d ON u.department_id = d.id 
                {$where_clause}";

$total_result = $database->fetch($total_query, $params);
$total_records = $total_result ? $total_result['count'] : 0;
$total_pages = ceil($total_records / $per_page);

$leaves_query = "SELECT l.*, u.name as employee_name, u.email as employee_email,
                        d.name as department_name,
                        admin.name as approved_by_name
                 FROM leaves l 
                 JOIN users u ON l.user_id = u.id 
                 LEFT JOIN departments d ON u.department_id = d.id
                 LEFT JOIN users admin ON l.approved_by = admin.id
                 {$where_clause}
                 ORDER BY l.created_at DESC 
                 LIMIT {$per_page} OFFSET {$offset}";

$leaves = $database->fetchAll($leaves_query, $params);

// Get departments for filter
$departments = getDepartments();

$page_title = 'Leave Requests Management';
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-alt me-2"></i>
                Leave Requests Management
            </h1>
            <div class="d-flex gap-2">
                <a href="reports.php" class="btn btn-outline-primary">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Status</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="department" class="form-label">Department</label>
                <select name="department" id="department" class="form-select">
                    <option value="all" <?php echo $department_filter === 'all' ? 'selected' : ''; ?>>All Departments</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" 
                                <?php echo $department_filter == $dept['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($dept['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Search by employee name or reason..." 
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Leave Requests Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                Leave Requests 
                <span class="badge bg-secondary"><?php echo $total_records; ?> total</span>
            </h5>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($leaves)): ?>
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No leave requests found</h5>
                <p class="text-muted">Try adjusting your filters or search criteria.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Employee</th>
                            <th>Department</th>
                            <th>Leave Period</th>
                            <th>Days</th>
                            <th>Status</th>
                            <th>Applied</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($leaves as $leave): ?>
                            <tr>
                                <td>
                                    <div class="fw-semibold"><?php echo htmlspecialchars($leave['employee_name']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($leave['employee_email']); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        <?php echo htmlspecialchars($leave['department_name'] ?? 'N/A'); ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?php echo formatDate($leave['from_date']); ?></div>
                                    <small class="text-muted">to <?php echo formatDate($leave['to_date']); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $leave['days_requested']; ?> days</span>
                                </td>
                                <td>
                                    <span class="badge status-<?php echo $leave['status']; ?>">
                                        <?php echo ucfirst($leave['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo formatDateTime($leave['created_at']); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="leave_details.php?id=<?php echo $leave['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        <?php if ($leave['status'] === 'pending'): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-success" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#approveModal"
                                                    data-leave-id="<?php echo $leave['id']; ?>"
                                                    data-employee-name="<?php echo htmlspecialchars($leave['employee_name']); ?>"
                                                    title="Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#rejectModal"
                                                    data-leave-id="<?php echo $leave['id']; ?>"
                                                    data-employee-name="<?php echo htmlspecialchars($leave['employee_name']); ?>"
                                                    title="Reject">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
<nav aria-label="Leave requests pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&department=<?php echo $department_filter; ?>&search=<?php echo urlencode($search); ?>">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        <?php endif; ?>

        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&department=<?php echo $department_filter; ?>&search=<?php echo urlencode($search); ?>">
                    <?php echo $i; ?>
                </a>
            </li>
        <?php endfor; ?>

        <?php if ($page < $total_pages): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&department=<?php echo $department_filter; ?>&search=<?php echo urlencode($search); ?>">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        <?php endif; ?>
    </ul>
</nav>
<?php endif; ?>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    Approve Leave Request
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="approve">
                    <input type="hidden" name="leave_id" id="approve_leave_id">

                    <div class="alert alert-success">
                        <i class="fas fa-info-circle me-2"></i>
                        You are about to approve the leave request for <strong id="approve_employee_name"></strong>.
                    </div>

                    <div class="mb-3">
                        <label for="approve_comments" class="form-label">Admin Comments (Optional)</label>
                        <textarea class="form-control" id="approve_comments" name="admin_comments" rows="3"
                                  placeholder="Add any comments or notes..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Approve Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times-circle text-danger me-2"></i>
                    Reject Leave Request
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="reject">
                    <input type="hidden" name="leave_id" id="reject_leave_id">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        You are about to reject the leave request for <strong id="reject_employee_name"></strong>.
                    </div>

                    <div class="mb-3">
                        <label for="reject_comments" class="form-label">Reason for Rejection *</label>
                        <textarea class="form-control" id="reject_comments" name="admin_comments" rows="3"
                                  placeholder="Please provide a reason for rejection..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>Reject Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle approve modal
    const approveModal = document.getElementById('approveModal');
    approveModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const leaveId = button.getAttribute('data-leave-id');
        const employeeName = button.getAttribute('data-employee-name');

        document.getElementById('approve_leave_id').value = leaveId;
        document.getElementById('approve_employee_name').textContent = employeeName;
    });

    // Handle reject modal
    const rejectModal = document.getElementById('rejectModal');
    rejectModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const leaveId = button.getAttribute('data-leave-id');
        const employeeName = button.getAttribute('data-employee-name');

        document.getElementById('reject_leave_id').value = leaveId;
        document.getElementById('reject_employee_name').textContent = employeeName;
    });
});
</script>

<?php include '../includes/footer.php'; ?>
