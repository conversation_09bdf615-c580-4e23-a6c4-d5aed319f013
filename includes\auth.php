<?php
/**
 * Authentication Functions
 * Employee Leave Management System
 */

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return isLoggedIn() && $_SESSION['user_role'] === 'admin';
}

/**
 * Check if user is employee
 */
function isEmployee() {
    return isLoggedIn() && $_SESSION['user_role'] === 'employee';
}

/**
 * Get current user ID
 */
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get current user data
 */
function getCurrentUser() {
    if (!isLoggedIn()) return null;
    
    global $database;
    return $database->fetch(
        "SELECT u.*, d.name as department_name 
         FROM users u 
         LEFT JOIN departments d ON u.department_id = d.id 
         WHERE u.id = ?",
        [getCurrentUserId()]
    );
}

/**
 * Login user
 */
function loginUser($email, $password) {
    global $database;
    
    // Get user by email
    $user = $database->fetch(
        "SELECT * FROM users WHERE email = ? AND is_active = 1",
        [$email]
    );
    
    if (!$user) {
        return ['success' => false, 'message' => 'Invalid email or password'];
    }
    
    // Verify password
    if (!password_verify($password, $user['password'])) {
        return ['success' => false, 'message' => 'Invalid email or password'];
    }
    
    // Create session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['department_id'] = $user['department_id'];
    $_SESSION['login_time'] = time();
    
    // Update last login
    $database->execute(
        "UPDATE users SET updated_at = NOW() WHERE id = ?",
        [$user['id']]
    );
    
    // Log activity
    logActivity($user['id'], 'login', 'User logged in');
    
    return ['success' => true, 'user' => $user];
}

/**
 * Register new user
 */
function registerUser($data) {
    global $database;
    
    // Validate required fields
    $required = ['name', 'email', 'password', 'department_id', 'address'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            return ['success' => false, 'message' => "Field '$field' is required"];
        }
    }
    
    // Validate email format
    if (!isValidEmail($data['email'])) {
        return ['success' => false, 'message' => 'Invalid email format'];
    }
    
    // Check if email already exists
    $existing = $database->fetch(
        "SELECT id FROM users WHERE email = ?",
        [$data['email']]
    );
    
    if ($existing) {
        return ['success' => false, 'message' => 'Email already registered'];
    }
    
    // Validate department
    $department = $database->fetch(
        "SELECT id FROM departments WHERE id = ?",
        [$data['department_id']]
    );
    
    if (!$department) {
        return ['success' => false, 'message' => 'Invalid department selected'];
    }
    
    // Hash password
    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
    
    // Insert user
    try {
        $database->execute(
            "INSERT INTO users (name, email, password, role, department_id, address, phone, hire_date) 
             VALUES (?, ?, ?, 'employee', ?, ?, ?, ?)",
            [
                $data['name'],
                $data['email'],
                $hashedPassword,
                $data['department_id'],
                $data['address'],
                $data['phone'] ?? null,
                $data['hire_date'] ?? date('Y-m-d')
            ]
        );
        
        $userId = $database->lastInsertId();
        
        // Initialize leave balances for current year
        initializeUserLeaveBalances($userId);
        
        // Log activity
        logActivity($userId, 'register', 'User registered');
        
        return ['success' => true, 'user_id' => $userId];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Registration failed: ' . $e->getMessage()];
    }
}

/**
 * Initialize leave balances for a new user
 */
function initializeUserLeaveBalances($userId) {
    global $database;
    
    $user = $database->fetch("SELECT hire_date FROM users WHERE id = ?", [$userId]);
    if (!$user) return;
    
    $hireDate = new DateTime($user['hire_date']);
    $currentYear = date('Y');
    $currentMonth = date('n');
    
    // Initialize balances from hire month to current month
    $startYear = $hireDate->format('Y');
    $startMonth = $hireDate->format('n');
    
    for ($year = $startYear; $year <= $currentYear; $year++) {
        $monthStart = ($year == $startYear) ? $startMonth : 1;
        $monthEnd = ($year == $currentYear) ? $currentMonth : 12;
        
        for ($month = $monthStart; $month <= $monthEnd; $month++) {
            $database->execute(
                "INSERT IGNORE INTO leave_balances (user_id, month, year, days_earned, total_balance) 
                 VALUES (?, ?, ?, ?, ?)",
                [$userId, $month, $year, MONTHLY_LEAVE_DAYS, MONTHLY_LEAVE_DAYS]
            );
        }
    }
}

/**
 * Logout user
 */
function logoutUser() {
    if (isLoggedIn()) {
        logActivity(getCurrentUserId(), 'logout', 'User logged out');
    }
    
    // Destroy session
    session_destroy();
    
    // Redirect to login
    header('Location: ' . BASE_URL . 'login.php');
    exit();
}

/**
 * Check session timeout
 */
function checkSessionTimeout() {
    if (isLoggedIn()) {
        $loginTime = $_SESSION['login_time'] ?? 0;
        if (time() - $loginTime > SESSION_TIMEOUT) {
            logoutUser();
        }
    }
}

/**
 * Require authentication for protected pages
 */
function requireAuth() {
    checkSessionTimeout();
    
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . 'login.php');
        exit();
    }
}

/**
 * Require admin role
 */
function requireAdmin() {
    requireAuth();
    
    if (!isAdmin()) {
        header('Location: ' . BASE_URL . 'employee/dashboard.php');
        exit();
    }
}

/**
 * Change user password
 */
function changePassword($userId, $currentPassword, $newPassword) {
    global $database;
    
    // Get current user
    $user = $database->fetch("SELECT password FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        return ['success' => false, 'message' => 'User not found'];
    }
    
    // Verify current password
    if (!password_verify($currentPassword, $user['password'])) {
        return ['success' => false, 'message' => 'Current password is incorrect'];
    }
    
    // Validate new password
    if (strlen($newPassword) < 6) {
        return ['success' => false, 'message' => 'New password must be at least 6 characters'];
    }
    
    // Update password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    $database->execute(
        "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?",
        [$hashedPassword, $userId]
    );
    
    // Log activity
    logActivity($userId, 'password_change', 'Password changed');
    
    return ['success' => true, 'message' => 'Password changed successfully'];
}
?>
