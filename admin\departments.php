<?php
require_once '../config/config.php';
requireAdmin();

// Handle department operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $_SESSION['error_message'] = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add') {
            $name = sanitize($_POST['name'] ?? '');
            $description = sanitize($_POST['description'] ?? '');
            
            if (empty($name)) {
                $_SESSION['error_message'] = 'Department name is required.';
            } else {
                // Check if department already exists
                $existing = $database->fetch(
                    "SELECT id FROM departments WHERE name = ?",
                    [$name]
                );
                
                if ($existing) {
                    $_SESSION['error_message'] = 'Department with this name already exists.';
                } else {
                    try {
                        $database->execute(
                            "INSERT INTO departments (name, description) VALUES (?, ?)",
                            [$name, $description]
                        );
                        
                        logActivity(getCurrentUserId(), 'department_add', "Added department: {$name}");
                        $_SESSION['success_message'] = 'Department added successfully.';
                    } catch (Exception $e) {
                        $_SESSION['error_message'] = 'Error adding department: ' . $e->getMessage();
                    }
                }
            }
        } elseif ($action === 'edit') {
            $id = (int)($_POST['id'] ?? 0);
            $name = sanitize($_POST['name'] ?? '');
            $description = sanitize($_POST['description'] ?? '');
            
            if ($id <= 0 || empty($name)) {
                $_SESSION['error_message'] = 'Invalid department data.';
            } else {
                // Check if another department has the same name
                $existing = $database->fetch(
                    "SELECT id FROM departments WHERE name = ? AND id != ?",
                    [$name, $id]
                );
                
                if ($existing) {
                    $_SESSION['error_message'] = 'Another department with this name already exists.';
                } else {
                    try {
                        $database->execute(
                            "UPDATE departments SET name = ?, description = ?, updated_at = NOW() WHERE id = ?",
                            [$name, $description, $id]
                        );
                        
                        logActivity(getCurrentUserId(), 'department_edit', "Edited department: {$name}");
                        $_SESSION['success_message'] = 'Department updated successfully.';
                    } catch (Exception $e) {
                        $_SESSION['error_message'] = 'Error updating department: ' . $e->getMessage();
                    }
                }
            }
        } elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);
            
            if ($id <= 0) {
                $_SESSION['error_message'] = 'Invalid department ID.';
            } else {
                // Check if department has employees
                $employee_count = $database->fetch(
                    "SELECT COUNT(*) as count FROM users WHERE department_id = ? AND is_active = 1",
                    [$id]
                )['count'];
                
                if ($employee_count > 0) {
                    $_SESSION['error_message'] = "Cannot delete department. It has {$employee_count} active employees.";
                } else {
                    try {
                        $dept = $database->fetch("SELECT name FROM departments WHERE id = ?", [$id]);
                        
                        $database->execute("DELETE FROM departments WHERE id = ?", [$id]);
                        
                        logActivity(getCurrentUserId(), 'department_delete', "Deleted department: " . ($dept['name'] ?? 'Unknown'));
                        $_SESSION['success_message'] = 'Department deleted successfully.';
                    } catch (Exception $e) {
                        $_SESSION['error_message'] = 'Error deleting department: ' . $e->getMessage();
                    }
                }
            }
        }
    }
    
    header('Location: departments.php');
    exit();
}

// Get departments with employee counts
$departments = $database->fetchAll(
    "SELECT d.*, 
            COUNT(u.id) as employee_count,
            COUNT(CASE WHEN u.is_active = 1 THEN 1 END) as active_employees
     FROM departments d
     LEFT JOIN users u ON d.id = u.department_id AND u.role = 'employee'
     GROUP BY d.id, d.name, d.description, d.created_at, d.updated_at
     ORDER BY d.name"
);

$page_title = 'Department Management';
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-building me-2"></i>
                Department Management
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
                <i class="fas fa-plus me-2"></i>Add Department
            </button>
        </div>
    </div>
</div>

<!-- Departments Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            Departments 
            <span class="badge bg-secondary"><?php echo count($departments); ?> total</span>
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($departments)): ?>
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No departments found</h5>
                <p class="text-muted mb-3">Start by adding your first department.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
                    <i class="fas fa-plus me-2"></i>Add Department
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Department Name</th>
                            <th>Description</th>
                            <th>Employees</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($departments as $dept): ?>
                            <tr>
                                <td>
                                    <div class="fw-semibold"><?php echo htmlspecialchars($dept['name']); ?></div>
                                </td>
                                <td>
                                    <div class="text-muted">
                                        <?php if (!empty($dept['description'])): ?>
                                            <?php echo htmlspecialchars(substr($dept['description'], 0, 100)); ?>
                                            <?php if (strlen($dept['description']) > 100): ?>...<?php endif; ?>
                                        <?php else: ?>
                                            <em>No description</em>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $dept['active_employees']; ?> active</span>
                                    <?php if ($dept['employee_count'] > $dept['active_employees']): ?>
                                        <span class="badge bg-secondary"><?php echo $dept['employee_count'] - $dept['active_employees']; ?> inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo formatDateTime($dept['created_at']); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editDepartmentModal"
                                                data-id="<?php echo $dept['id']; ?>"
                                                data-name="<?php echo htmlspecialchars($dept['name']); ?>"
                                                data-description="<?php echo htmlspecialchars($dept['description']); ?>"
                                                title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        
                                        <?php if ($dept['active_employees'] == 0): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger btn-delete" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteDepartmentModal"
                                                    data-id="<?php echo $dept['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($dept['name']); ?>"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-secondary" 
                                                    disabled
                                                    title="Cannot delete - has active employees">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Department Modal -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle text-primary me-2"></i>
                    Add New Department
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label for="add_name" class="form-label">Department Name *</label>
                        <input type="text" 
                               class="form-control" 
                               id="add_name" 
                               name="name" 
                               required>
                        <div class="invalid-feedback">
                            Please provide a department name.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description</label>
                        <textarea class="form-control" 
                                  id="add_description" 
                                  name="description" 
                                  rows="3"
                                  placeholder="Optional description of the department..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Department
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Department Modal -->
<div class="modal fade" id="editDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit text-warning me-2"></i>
                    Edit Department
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Department Name *</label>
                        <input type="text" 
                               class="form-control" 
                               id="edit_name" 
                               name="name" 
                               required>
                        <div class="invalid-feedback">
                            Please provide a department name.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" 
                                  id="edit_description" 
                                  name="description" 
                                  rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>Update Department
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Department Modal -->
<div class="modal fade" id="deleteDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash text-danger me-2"></i>
                    Delete Department
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="delete_id">
                    
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Are you sure you want to delete the department "<strong id="delete_name"></strong>"?
                        <br><br>
                        <strong>This action cannot be undone!</strong>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Department
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit modal
    const editModal = document.getElementById('editDepartmentModal');
    editModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const id = button.getAttribute('data-id');
        const name = button.getAttribute('data-name');
        const description = button.getAttribute('data-description');
        
        document.getElementById('edit_id').value = id;
        document.getElementById('edit_name').value = name;
        document.getElementById('edit_description').value = description;
    });
    
    // Handle delete modal
    const deleteModal = document.getElementById('deleteDepartmentModal');
    deleteModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const id = button.getAttribute('data-id');
        const name = button.getAttribute('data-name');
        
        document.getElementById('delete_id').value = id;
        document.getElementById('delete_name').textContent = name;
    });
});
</script>

<?php include '../includes/footer.php'; ?>
