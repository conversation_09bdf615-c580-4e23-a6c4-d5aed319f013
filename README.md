# Employee Leave Management System

A comprehensive web-based leave management system built for Southern Technical University Presidency using PHP, MySQL, HTML, CSS, and Bootstrap.

## 🌟 Features

### General Features
- **Responsive Design**: Mobile-friendly interface using Bootstrap 5
- **Role-based Access Control**: Admin and Employee roles with different permissions
- **Secure Authentication**: Login/logout with session management and CSRF protection
- **Real-time Calculations**: Automatic leave balance calculations (3 days/month)
- **Modern UI**: Clean, professional interface with intuitive navigation

### Employee Features
- ✅ **Dashboard**: Overview of leave balance and recent requests
- ✅ **Apply for Leave**: Submit leave requests with validation
- ✅ **Leave History**: View all past leave requests with filtering
- ✅ **Profile Management**: Update personal information
- ✅ **Balance Tracking**: Real-time leave balance with monthly breakdown
- ✅ **Substitute Information**: Specify substitute during leave

### Admin Features
- ✅ **Admin Dashboard**: System overview with statistics and charts
- ✅ **Employee Management**: View all employees and their details
- ✅ **Leave Approval**: Approve/reject leave requests with comments
- ✅ **Department Management**: Add/edit/delete departments
- ✅ **Comprehensive Reports**: Department statistics and leave trends
- ✅ **System Analytics**: Visual charts and data insights

### Leave Management Logic
- **Monthly Allocation**: 3 leave days earned per month
- **Carry Forward**: Unused leaves accumulate for end-of-service reward
- **Working Days**: Automatic calculation excluding weekends
- **Validation**: Prevents overlapping requests and insufficient balance
- **Status Tracking**: Pending, Approved, Rejected status management

## 🛠️ Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, Bootstrap 5.3
- **JavaScript**: Vanilla JS with Chart.js for analytics
- **Icons**: Font Awesome 6.4
- **Security**: CSRF protection, prepared statements, input validation

## 📋 Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- Modern web browser

## 🚀 Installation

### 1. Clone/Download the Project
```bash
git clone <repository-url>
# or download and extract the ZIP file
```

### 2. Database Setup
1. Create a new MySQL database named `employee_leave_system`
2. Import the database schema:
   ```bash
   mysql -u username -p employee_leave_system < database/schema.sql
   ```
3. Import sample data (optional):
   ```bash
   mysql -u username -p employee_leave_system < database/sample_data.sql
   ```

### 3. Configuration
1. Update database credentials in `config/database.php`:
   ```php
   private $host = 'localhost';
   private $db_name = 'employee_leave_system';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

2. Update base URL in `config/config.php`:
   ```php
   define('BASE_URL', 'http://your-domain.com/path-to-project/');
   ```

### 4. Web Server Setup
- Place the project files in your web server's document root
- Ensure PHP has write permissions for session handling
- Configure virtual host (optional but recommended)

### 5. Access the System
- Navigate to your project URL in a web browser
- Use the demo credentials or register a new employee account

## 👥 Demo Credentials

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123

### Employee Account
- **Email**: <EMAIL>
- **Password**: password123

## 📁 Project Structure

```
employee-leave-system/
├── admin/                  # Admin panel pages
│   ├── dashboard.php
│   ├── leaves.php
│   ├── departments.php
│   └── reports.php
├── employee/              # Employee panel pages
│   ├── dashboard.php
│   ├── apply_leave.php
│   ├── leave_history.php
│   └── profile.php
├── config/                # Configuration files
│   ├── config.php
│   └── database.php
├── includes/              # Common includes
│   ├── header.php
│   ├── footer.php
│   ├── functions.php
│   ├── auth.php
│   ├── security.php
│   └── leave_calculations.php
├── assets/                # Static assets
│   ├── css/
│   ├── js/
│   └── images/
├── database/              # Database files
│   ├── schema.sql
│   └── sample_data.sql
├── index.php              # Landing page
├── login.php              # Login page
├── register.php           # Registration page
├── logout.php             # Logout handler
└── README.md              # This file
```

## 🔒 Security Features

- **CSRF Protection**: All forms protected against CSRF attacks
- **SQL Injection Prevention**: Prepared statements for all database queries
- **XSS Protection**: Input sanitization and output encoding
- **Session Security**: Secure session handling with timeout
- **Rate Limiting**: Login attempt rate limiting
- **Input Validation**: Comprehensive server-side validation
- **Security Headers**: Proper HTTP security headers

## 📊 Database Schema

### Main Tables
- **users**: Employee and admin accounts
- **departments**: University departments
- **leaves**: Leave requests and their status
- **leave_balances**: Monthly leave balance tracking
- **user_sessions**: Session management

### Key Relationships
- Users belong to departments
- Leaves belong to users
- Leave balances track monthly allocations per user

## 🎨 UI/UX Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern Interface**: Clean, professional design
- **Intuitive Navigation**: Easy-to-use menu structure
- **Visual Feedback**: Success/error messages and loading states
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Charts & Analytics**: Visual representation of data

## 🔧 Customization

### Adding New Features
1. Create new PHP files in appropriate directories
2. Update navigation in `includes/header.php`
3. Add database tables if needed
4. Implement proper security measures

### Styling Changes
- Modify `assets/css/style.css` for custom styles
- Update Bootstrap variables if needed
- Add custom JavaScript in `assets/js/script.js`

### Configuration Options
- Adjust leave calculation rules in `config/config.php`
- Modify email templates and notifications
- Update organization branding and information

## 🐛 Troubleshooting

### Common Issues
1. **Database Connection Error**: Check credentials in `config/database.php`
2. **Permission Denied**: Ensure proper file permissions
3. **Session Issues**: Check PHP session configuration
4. **CSS/JS Not Loading**: Verify asset paths in configuration

### Debug Mode
Enable debug mode in `config/config.php` for development:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📝 License

This project is developed for Southern Technical University Presidency. All rights reserved.

## 🤝 Support

For technical support or questions:
- Email: <EMAIL>
- Phone: +964-123-456-789

## 📈 Future Enhancements

- Email notifications for leave requests
- Mobile app integration
- Advanced reporting with PDF export
- Integration with HR systems
- Multi-language support
- Calendar integration
- Automated leave policy enforcement

---

**Developed for Southern Technical University Presidency**  
*Employee Leave Management System v1.0.0*
