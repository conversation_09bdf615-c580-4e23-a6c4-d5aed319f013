/**
 * Employee Leave Management System - Custom JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeComponents();
    initializeFormValidation();
    initializeDatePickers();
    initializeTooltips();
    initializeConfirmDialogs();
});

/**
 * Initialize all JavaScript components
 */
function initializeComponents() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert && alert.parentNode) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                // Focus on first invalid field
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
            
            form.classList.add('was-validated');
        }, false);
    });

    // Real-time validation for specific fields
    const emailFields = document.querySelectorAll('input[type="email"]');
    emailFields.forEach(function(field) {
        field.addEventListener('blur', function() {
            validateEmail(this);
        });
    });

    // Password confirmation validation
    const passwordFields = document.querySelectorAll('input[name="password"]');
    const confirmPasswordFields = document.querySelectorAll('input[name="confirm_password"]');
    
    if (passwordFields.length > 0 && confirmPasswordFields.length > 0) {
        confirmPasswordFields.forEach(function(confirmField) {
            confirmField.addEventListener('input', function() {
                const passwordField = document.querySelector('input[name="password"]');
                validatePasswordMatch(passwordField, this);
            });
        });
    }
}

/**
 * Initialize date pickers and validation
 */
function initializeDatePickers() {
    const fromDateFields = document.querySelectorAll('input[name="from_date"]');
    const toDateFields = document.querySelectorAll('input[name="to_date"]');
    
    fromDateFields.forEach(function(fromDate) {
        fromDate.addEventListener('change', function() {
            const toDate = document.querySelector('input[name="to_date"]');
            if (toDate) {
                toDate.min = this.value;
                if (toDate.value && toDate.value < this.value) {
                    toDate.value = this.value;
                }
                calculateLeaveDays();
            }
        });
    });
    
    toDateFields.forEach(function(toDate) {
        toDate.addEventListener('change', function() {
            const fromDate = document.querySelector('input[name="from_date"]');
            if (fromDate && this.value < fromDate.value) {
                this.value = fromDate.value;
            }
            calculateLeaveDays();
        });
    });

    // Set minimum date to today for future date fields
    const futureDateFields = document.querySelectorAll('.future-date');
    const today = new Date().toISOString().split('T')[0];
    futureDateFields.forEach(function(field) {
        field.min = today;
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Initialize confirmation dialogs
 */
function initializeConfirmDialogs() {
    // Delete confirmations
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-delete') || e.target.closest('.btn-delete')) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
        }
        
        // Approve/Reject confirmations
        if (e.target.classList.contains('btn-approve') || e.target.closest('.btn-approve')) {
            if (!confirm('Are you sure you want to approve this leave request?')) {
                e.preventDefault();
                return false;
            }
        }
        
        if (e.target.classList.contains('btn-reject') || e.target.closest('.btn-reject')) {
            if (!confirm('Are you sure you want to reject this leave request?')) {
                e.preventDefault();
                return false;
            }
        }
    });
}

/**
 * Validate email format
 */
function validateEmail(field) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(field.value);
    
    if (field.value && !isValid) {
        field.setCustomValidity('Please enter a valid email address');
        field.classList.add('is-invalid');
    } else {
        field.setCustomValidity('');
        field.classList.remove('is-invalid');
    }
}

/**
 * Validate password match
 */
function validatePasswordMatch(passwordField, confirmField) {
    if (passwordField.value !== confirmField.value) {
        confirmField.setCustomValidity('Passwords do not match');
        confirmField.classList.add('is-invalid');
    } else {
        confirmField.setCustomValidity('');
        confirmField.classList.remove('is-invalid');
    }
}

/**
 * Calculate leave days between two dates
 */
function calculateLeaveDays() {
    const fromDate = document.querySelector('input[name="from_date"]');
    const toDate = document.querySelector('input[name="to_date"]');
    const daysDisplay = document.getElementById('leave-days-display');
    
    if (fromDate && toDate && fromDate.value && toDate.value && daysDisplay) {
        const start = new Date(fromDate.value);
        const end = new Date(toDate.value);
        
        if (end >= start) {
            const timeDiff = end.getTime() - start.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
            
            daysDisplay.textContent = daysDiff + ' day' + (daysDiff !== 1 ? 's' : '');
            daysDisplay.className = 'badge bg-primary';
        } else {
            daysDisplay.textContent = 'Invalid date range';
            daysDisplay.className = 'badge bg-danger';
        }
    }
}

/**
 * Toggle password visibility
 */
function togglePasswordVisibility(buttonId, fieldId) {
    const button = document.getElementById(buttonId);
    const field = document.getElementById(fieldId);
    
    if (button && field) {
        button.addEventListener('click', function() {
            const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
            field.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            }
        });
    }
}

/**
 * Show loading spinner on form submit
 */
function showLoadingSpinner(formId, buttonId) {
    const form = document.getElementById(formId);
    const button = document.getElementById(buttonId);
    
    if (form && button) {
        form.addEventListener('submit', function() {
            if (form.checkValidity()) {
                button.disabled = true;
                button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Processing...';
            }
        });
    }
}

/**
 * Auto-resize textarea
 */
function autoResizeTextarea() {
    const textareas = document.querySelectorAll('textarea.auto-resize');
    
    textareas.forEach(function(textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    });
}

/**
 * Format numbers with commas
 */
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * Format date for display
 */
function formatDate(dateString, format = 'dd/mm/yyyy') {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    
    switch (format) {
        case 'dd/mm/yyyy':
            return `${day}/${month}/${year}`;
        case 'mm/dd/yyyy':
            return `${month}/${day}/${year}`;
        case 'yyyy-mm-dd':
            return `${year}-${month}-${day}`;
        default:
            return `${day}/${month}/${year}`;
    }
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    showAlert(message, 'success');
}

/**
 * Show error message
 */
function showErrorMessage(message) {
    showAlert(message, 'danger');
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || document.querySelector('.container');
    
    if (alertContainer) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            if (alertDiv && alertDiv.parentNode) {
                const bsAlert = new bootstrap.Alert(alertDiv);
                bsAlert.close();
            }
        }, 5000);
    }
}

/**
 * Get alert icon based on type
 */
function getAlertIcon(type) {
    switch (type) {
        case 'success':
            return 'check-circle';
        case 'danger':
            return 'exclamation-circle';
        case 'warning':
            return 'exclamation-triangle';
        case 'info':
            return 'info-circle';
        default:
            return 'info-circle';
    }
}

/**
 * Initialize charts (if Chart.js is available)
 */
function initializeCharts() {
    if (typeof Chart !== 'undefined') {
        // Chart configurations will be added in specific pages
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        Chart.defaults.color = '#6c757d';
    }
}

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeCharts);

// Export functions for use in other scripts
window.LeaveManagement = {
    showSuccessMessage,
    showErrorMessage,
    showAlert,
    formatDate,
    formatNumber,
    calculateLeaveDays,
    togglePasswordVisibility,
    showLoadingSpinner
};
