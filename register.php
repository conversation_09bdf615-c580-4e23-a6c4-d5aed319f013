<?php
require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isAdmin()) {
        header('Location: admin/dashboard.php');
    } else {
        header('Location: employee/dashboard.php');
    }
    exit();
}

$error_message = '';
$success_message = '';
$departments = getDepartments();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $data = [
            'name' => sanitize($_POST['name'] ?? ''),
            'email' => sanitize($_POST['email'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'department_id' => sanitize($_POST['department_id'] ?? ''),
            'address' => sanitize($_POST['address'] ?? ''),
            'phone' => sanitize($_POST['phone'] ?? ''),
            'hire_date' => sanitize($_POST['hire_date'] ?? '')
        ];
        
        // Validation
        if (empty($data['name']) || empty($data['email']) || empty($data['password']) || 
            empty($data['department_id']) || empty($data['address'])) {
            $error_message = 'Please fill in all required fields.';
        } elseif (!isValidEmail($data['email'])) {
            $error_message = 'Please provide a valid email address.';
        } elseif (strlen($data['password']) < 6) {
            $error_message = 'Password must be at least 6 characters long.';
        } elseif ($data['password'] !== $data['confirm_password']) {
            $error_message = 'Passwords do not match.';
        } elseif (!empty($data['hire_date']) && !isValidDate($data['hire_date'])) {
            $error_message = 'Please provide a valid hire date.';
        } else {
            // Set default hire date if not provided
            if (empty($data['hire_date'])) {
                $data['hire_date'] = date('Y-m-d');
            }
            
            $result = registerUser($data);
            
            if ($result['success']) {
                $success_message = 'Registration successful! You can now login with your credentials.';
                // Clear form data
                $data = [];
            } else {
                $error_message = $result['message'];
            }
        }
    }
}

$page_title = 'Employee Registration';
include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-7">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-success text-white text-center py-4">
                <h3 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Employee Registration
                </h3>
                <p class="mb-0 mt-2">Join Southern Technical University Leave Management System</p>
            </div>
            <div class="card-body p-5">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        <div class="mt-3">
                            <a href="login.php" class="btn btn-success">
                                <i class="fas fa-sign-in-alt me-2"></i>Login Now
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                
                <form method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-2"></i>Full Name *
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   value="<?php echo htmlspecialchars($data['name'] ?? ''); ?>"
                                   required>
                            <div class="invalid-feedback">
                                Please provide your full name.
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email Address *
                            </label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo htmlspecialchars($data['email'] ?? ''); ?>"
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid email address.
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password *
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   minlength="6"
                                   required>
                            <div class="invalid-feedback">
                                Password must be at least 6 characters long.
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Confirm Password *
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required>
                            <div class="invalid-feedback">
                                Please confirm your password.
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="department_id" class="form-label">
                                <i class="fas fa-building me-2"></i>Department *
                            </label>
                            <select class="form-select" id="department_id" name="department_id" required>
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>" 
                                            <?php echo (isset($data['department_id']) && $data['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select your department.
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-2"></i>Phone Number
                            </label>
                            <input type="tel" 
                                   class="form-control" 
                                   id="phone" 
                                   name="phone" 
                                   value="<?php echo htmlspecialchars($data['phone'] ?? ''); ?>"
                                   placeholder="+964-XXX-XXX-XXXX">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">
                            <i class="fas fa-map-marker-alt me-2"></i>Address *
                        </label>
                        <textarea class="form-control" 
                                  id="address" 
                                  name="address" 
                                  rows="3" 
                                  required><?php echo htmlspecialchars($data['address'] ?? ''); ?></textarea>
                        <div class="invalid-feedback">
                            Please provide your address.
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="hire_date" class="form-label">
                            <i class="fas fa-calendar me-2"></i>Hire Date
                        </label>
                        <input type="date" 
                               class="form-control" 
                               id="hire_date" 
                               name="hire_date" 
                               value="<?php echo htmlspecialchars($data['hire_date'] ?? date('Y-m-d')); ?>"
                               max="<?php echo date('Y-m-d'); ?>">
                        <small class="form-text text-muted">Leave blank to use today's date</small>
                    </div>
                    
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-user-plus me-2"></i>
                            Register
                        </button>
                    </div>
                </form>
                
                <?php endif; ?>
                
                <div class="text-center">
                    <p class="mb-0">Already have an account?</p>
                    <a href="login.php" class="btn btn-link">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Login Here
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('change', validatePassword);
    confirmPassword.addEventListener('keyup', validatePassword);
});
</script>

<?php include 'includes/footer.php'; ?>
