        </div>
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-university me-2"></i><?php echo ORGANIZATION; ?></h5>
                    <p class="mb-0">Employee Leave Management System</p>
                    <small class="text-muted">Version <?php echo APP_VERSION; ?></small>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        <a href="mailto:<EMAIL>" class="text-light text-decoration-none"><EMAIL></a>
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        <a href="tel:+964-123-456-789" class="text-light text-decoration-none">+964-123-456-789</a>
                    </p>
                    <small class="text-muted">
                        &copy; <?php echo date('Y'); ?> Southern Technical University. All rights reserved.
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js for reports -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_URL; ?>js/script.js"></script>
    
    <!-- Page-specific scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php foreach ($page_scripts as $script): ?>
            <script src="<?php echo ASSETS_URL; ?>js/<?php echo $script; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <script>
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
        
        // Confirm delete actions
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-delete') || e.target.closest('.btn-delete')) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                }
            }
        });
        
        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('.needs-validation');
            forms.forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    if (!form.checkValidity()) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            });
        });
        
        // Date validation for leave forms
        function validateLeaveDates() {
            const fromDate = document.getElementById('from_date');
            const toDate = document.getElementById('to_date');
            
            if (fromDate && toDate) {
                fromDate.addEventListener('change', function() {
                    toDate.min = this.value;
                    if (toDate.value && toDate.value < this.value) {
                        toDate.value = this.value;
                    }
                });
                
                toDate.addEventListener('change', function() {
                    if (this.value < fromDate.value) {
                        this.value = fromDate.value;
                    }
                });
            }
        }
        
        // Initialize date validation
        document.addEventListener('DOMContentLoaded', validateLeaveDates);
    </script>
</body>
</html>
