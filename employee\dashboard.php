<?php
require_once '../config/config.php';
requireAuth();

// Ensure user is employee
if (!isEmployee()) {
    header('Location: ../admin/dashboard.php');
    exit();
}

$user_id = getCurrentUserId();
$current_user = getCurrentUser();

// Calculate current leave balance
$current_balance = calculateLeaveBalance($user_id);

// Get leave statistics
$stats = [];

// Total leave requests
$totalRequestsResult = $database->fetch(
    "SELECT COUNT(*) as count FROM leaves WHERE user_id = ?",
    [$user_id]
);
$stats['total_requests'] = $totalRequestsResult ? $totalRequestsResult['count'] : 0;

// Pending requests
$pendingRequestsResult = $database->fetch(
    "SELECT COUNT(*) as count FROM leaves WHERE user_id = ? AND status = 'pending'",
    [$user_id]
);
$stats['pending_requests'] = $pendingRequestsResult ? $pendingRequestsResult['count'] : 0;

// Approved requests this year
$approvedThisYearResult = $database->fetch(
    "SELECT COUNT(*) as count FROM leaves 
     WHERE user_id = ? AND status = 'approved' AND YEAR(from_date) = YEAR(CURRENT_DATE())",
    [$user_id]
);
$stats['approved_this_year'] = $approvedThisYearResult ? $approvedThisYearResult['count'] : 0;

// Days used this year
$daysUsedResult = $database->fetch(
    "SELECT COALESCE(SUM(days_requested), 0) as total_days 
     FROM leaves 
     WHERE user_id = ? AND status = 'approved' AND YEAR(from_date) = YEAR(CURRENT_DATE())",
    [$user_id]
);
$stats['days_used_this_year'] = $daysUsedResult ? $daysUsedResult['total_days'] : 0;

// Recent leave requests
$recent_requests = $database->fetchAll(
    "SELECT * FROM leaves 
     WHERE user_id = ? 
     ORDER BY created_at DESC 
     LIMIT 5",
    [$user_id]
);

// Monthly leave balance for current year
$monthly_balances = $database->fetchAll(
    "SELECT month, days_earned, days_used, total_balance 
     FROM leave_balances 
     WHERE user_id = ? AND year = YEAR(CURRENT_DATE())
     ORDER BY month",
    [$user_id]
);

// Calculate total earned this year
$total_earned_this_year = 0;
foreach ($monthly_balances as $balance) {
    $total_earned_this_year += $balance['days_earned'];
}

$page_title = 'Employee Dashboard';
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Welcome, <?php echo htmlspecialchars($current_user['name']); ?>
                </h1>
                <p class="text-muted mb-0">
                    <i class="fas fa-building me-1"></i>
                    <?php echo htmlspecialchars($current_user['department_name'] ?? 'No Department'); ?>
                </p>
            </div>
            <div class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                <?php echo date('l, F j, Y'); ?>
            </div>
        </div>
    </div>
</div>

<!-- Leave Balance Card -->
<div class="row g-4 mb-4">
    <div class="col-lg-4">
        <div class="card dashboard-card border-0 h-100 bg-primary text-white">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-calendar-check fa-3x opacity-75"></i>
                </div>
                <h2 class="display-4 fw-bold mb-2"><?php echo number_format($current_balance, 1); ?></h2>
                <h5 class="mb-0">Available Leave Days</h5>
                <small class="opacity-75">As of <?php echo date('F Y'); ?></small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <div class="row g-4 h-100">
            <div class="col-md-4">
                <div class="card dashboard-card border-0 h-100">
                    <div class="card-body text-center">
                        <div class="text-success mb-2">
                            <i class="fas fa-plus-circle fa-2x"></i>
                        </div>
                        <h4 class="fw-bold text-success"><?php echo $total_earned_this_year; ?></h4>
                        <small class="text-muted">Days Earned This Year</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card dashboard-card border-0 h-100">
                    <div class="card-body text-center">
                        <div class="text-info mb-2">
                            <i class="fas fa-minus-circle fa-2x"></i>
                        </div>
                        <h4 class="fw-bold text-info"><?php echo $stats['days_used_this_year']; ?></h4>
                        <small class="text-muted">Days Used This Year</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card dashboard-card border-0 h-100">
                    <div class="card-body text-center">
                        <div class="text-warning mb-2">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                        <h4 class="fw-bold text-warning"><?php echo $stats['pending_requests']; ?></h4>
                        <small class="text-muted">Pending Requests</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="apply_leave.php" class="btn btn-primary w-100 py-3">
                            <i class="fas fa-plus-circle fa-lg mb-2 d-block"></i>
                            Apply for Leave
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="leave_history.php" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-history fa-lg mb-2 d-block"></i>
                            Leave History
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="profile.php" class="btn btn-outline-secondary w-100 py-3">
                            <i class="fas fa-user fa-lg mb-2 d-block"></i>
                            Update Profile
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="../change_password.php" class="btn btn-outline-secondary w-100 py-3">
                            <i class="fas fa-key fa-lg mb-2 d-block"></i>
                            Change Password
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Recent Leave Requests -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Recent Leave Requests
                    </h5>
                    <a href="leave_history.php" class="btn btn-sm btn-outline-primary">
                        View All <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recent_requests)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No leave requests yet</h5>
                        <p class="text-muted mb-3">Start by applying for your first leave request.</p>
                        <a href="apply_leave.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Apply for Leave
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Leave Period</th>
                                    <th>Days</th>
                                    <th>Status</th>
                                    <th>Applied</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_requests as $request): ?>
                                    <tr>
                                        <td>
                                            <div><?php echo formatDate($request['from_date']); ?></div>
                                            <small class="text-muted">to <?php echo formatDate($request['to_date']); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $request['days_requested']; ?> days</span>
                                        </td>
                                        <td>
                                            <span class="badge status-<?php echo $request['status']; ?>">
                                                <?php echo ucfirst($request['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo formatDateTime($request['created_at']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <a href="leave_details.php?id=<?php echo $request['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Monthly Balance Overview -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    <?php echo date('Y'); ?> Balance Overview
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($monthly_balances)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-2x text-muted mb-3"></i>
                        <p class="text-muted">No balance data available</p>
                    </div>
                <?php else: ?>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <small class="text-muted">Leave Usage Progress</small>
                            <small class="text-muted">
                                <?php echo $stats['days_used_this_year']; ?>/<?php echo $total_earned_this_year; ?> days
                            </small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <?php 
                            $usage_percentage = $total_earned_this_year > 0 ? 
                                ($stats['days_used_this_year'] / $total_earned_this_year) * 100 : 0;
                            $progress_class = $usage_percentage > 80 ? 'bg-danger' : 
                                            ($usage_percentage > 60 ? 'bg-warning' : 'bg-success');
                            ?>
                            <div class="progress-bar <?php echo $progress_class; ?>" 
                                 style="width: <?php echo min(100, $usage_percentage); ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="small">
                        <?php 
                        $current_month = date('n');
                        foreach ($monthly_balances as $balance): 
                            if ($balance['month'] <= $current_month):
                        ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">
                                    <?php echo date('M', mktime(0, 0, 0, $balance['month'], 1)); ?>
                                </span>
                                <div class="text-end">
                                    <div class="fw-semibold">
                                        <?php echo number_format($balance['total_balance'], 1); ?> days
                                    </div>
                                    <small class="text-muted">
                                        +<?php echo $balance['days_earned']; ?> 
                                        <?php if ($balance['days_used'] > 0): ?>
                                            | -<?php echo $balance['days_used']; ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        <?php 
                            endif;
                        endforeach; 
                        ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
