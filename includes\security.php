<?php
/**
 * Security Functions
 * Employee Leave Management System
 */

/**
 * Rate limiting for login attempts
 */
class RateLimiter {
    private $database;
    private $max_attempts;
    private $lockout_time;
    
    public function __construct($database, $max_attempts = 5, $lockout_time = 900) { // 15 minutes
        $this->database = $database;
        $this->max_attempts = $max_attempts;
        $this->lockout_time = $lockout_time;
    }
    
    /**
     * Check if IP is rate limited
     */
    public function isRateLimited($ip) {
        // Clean old attempts
        $this->cleanOldAttempts();
        
        $attempts = $this->database->fetch(
            "SELECT COUNT(*) as count FROM login_attempts 
             WHERE ip_address = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$ip, $this->lockout_time]
        );
        
        return $attempts && $attempts['count'] >= $this->max_attempts;
    }
    
    /**
     * Record failed login attempt
     */
    public function recordAttempt($ip, $email = null) {
        $this->database->execute(
            "INSERT INTO login_attempts (ip_address, email, attempted_at) VALUES (?, ?, NOW())",
            [$ip, $email]
        );
    }
    
    /**
     * Clear attempts for successful login
     */
    public function clearAttempts($ip) {
        $this->database->execute(
            "DELETE FROM login_attempts WHERE ip_address = ?",
            [$ip]
        );
    }
    
    /**
     * Clean old login attempts
     */
    private function cleanOldAttempts() {
        $this->database->execute(
            "DELETE FROM login_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$this->lockout_time]
        );
    }
    
    /**
     * Get remaining lockout time
     */
    public function getRemainingLockoutTime($ip) {
        $latest = $this->database->fetch(
            "SELECT attempted_at FROM login_attempts 
             WHERE ip_address = ? 
             ORDER BY attempted_at DESC 
             LIMIT 1",
            [$ip]
        );
        
        if (!$latest) return 0;
        
        $lockout_until = strtotime($latest['attempted_at']) + $this->lockout_time;
        $remaining = $lockout_until - time();
        
        return max(0, $remaining);
    }
}

/**
 * Input validation and sanitization
 */
class InputValidator {
    
    /**
     * Validate email format
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate password strength
     */
    public static function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < 6) {
            $errors[] = 'Password must be at least 6 characters long';
        }
        
        if (!preg_match('/[A-Za-z]/', $password)) {
            $errors[] = 'Password must contain at least one letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        return $errors;
    }
    
    /**
     * Validate date format
     */
    public static function validateDate($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * Validate phone number
     */
    public static function validatePhone($phone) {
        // Remove all non-digit characters
        $cleaned = preg_replace('/[^0-9+]/', '', $phone);
        
        // Check if it's a valid format (basic validation)
        return preg_match('/^[\+]?[0-9]{10,15}$/', $cleaned);
    }
    
    /**
     * Sanitize HTML input
     */
    public static function sanitizeHtml($input) {
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate file upload
     */
    public static function validateFileUpload($file, $allowed_types = [], $max_size = 2097152) { // 2MB default
        $errors = [];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'File upload error';
            return $errors;
        }
        
        if ($file['size'] > $max_size) {
            $errors[] = 'File size exceeds maximum allowed size';
        }
        
        if (!empty($allowed_types)) {
            $file_type = mime_content_type($file['tmp_name']);
            if (!in_array($file_type, $allowed_types)) {
                $errors[] = 'File type not allowed';
            }
        }
        
        return $errors;
    }
}

/**
 * SQL Injection prevention helpers
 */
class DatabaseSecurity {
    
    /**
     * Prepare and execute query safely
     */
    public static function safeQuery($database, $query, $params = []) {
        try {
            $stmt = $database->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            throw new Exception("Database operation failed");
        }
    }
    
    /**
     * Validate table/column names (for dynamic queries)
     */
    public static function validateIdentifier($identifier) {
        return preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $identifier);
    }
}

/**
 * Session security
 */
class SessionSecurity {
    
    /**
     * Regenerate session ID
     */
    public static function regenerateId() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_regenerate_id(true);
        }
    }
    
    /**
     * Validate session
     */
    public static function validateSession() {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['login_time'])) {
            if (time() - $_SESSION['login_time'] > SESSION_TIMEOUT) {
                return false;
            }
        }
        
        // Check IP address (optional - can cause issues with mobile users)
        if (isset($_SESSION['ip_address'])) {
            if ($_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
                // Log suspicious activity
                error_log("Session IP mismatch for user " . $_SESSION['user_id']);
            }
        }
        
        return true;
    }
    
    /**
     * Destroy session securely
     */
    public static function destroySession() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            $_SESSION = [];
            
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }
            
            session_destroy();
        }
    }
}

/**
 * XSS Protection
 */
class XSSProtection {
    
    /**
     * Clean output for display
     */
    public static function clean($input) {
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Clean array recursively
     */
    public static function cleanArray($array) {
        $cleaned = [];
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $cleaned[$key] = self::cleanArray($value);
            } else {
                $cleaned[$key] = self::clean($value);
            }
        }
        return $cleaned;
    }
}

/**
 * File upload security
 */
class FileUploadSecurity {
    
    /**
     * Generate secure filename
     */
    public static function generateSecureFilename($original_name) {
        $extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $filename = uniqid() . '_' . time();
        
        if ($extension) {
            $filename .= '.' . strtolower($extension);
        }
        
        return $filename;
    }
    
    /**
     * Validate file type by content
     */
    public static function validateFileType($file_path, $allowed_types) {
        $file_type = mime_content_type($file_path);
        return in_array($file_type, $allowed_types);
    }
    
    /**
     * Scan file for malware (basic check)
     */
    public static function basicMalwareScan($file_path) {
        $content = file_get_contents($file_path);
        
        // Basic patterns to look for
        $malware_patterns = [
            '/<\?php.*eval\s*\(/i',
            '/base64_decode\s*\(/i',
            '/shell_exec\s*\(/i',
            '/system\s*\(/i',
            '/exec\s*\(/i',
            '/passthru\s*\(/i'
        ];
        
        foreach ($malware_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return false;
            }
        }
        
        return true;
    }
}

/**
 * Audit logging
 */
class AuditLogger {
    private $database;
    
    public function __construct($database) {
        $this->database = $database;
    }
    
    /**
     * Log security event
     */
    public function logSecurityEvent($event_type, $description, $user_id = null, $ip_address = null) {
        if ($ip_address === null) {
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        }
        
        $this->database->execute(
            "INSERT INTO security_logs (event_type, description, user_id, ip_address, created_at) 
             VALUES (?, ?, ?, ?, NOW())",
            [$event_type, $description, $user_id, $ip_address]
        );
    }
    
    /**
     * Log failed login
     */
    public function logFailedLogin($email, $ip_address) {
        $this->logSecurityEvent('failed_login', "Failed login attempt for email: {$email}", null, $ip_address);
    }
    
    /**
     * Log successful login
     */
    public function logSuccessfulLogin($user_id, $ip_address) {
        $this->logSecurityEvent('successful_login', "Successful login", $user_id, $ip_address);
    }
    
    /**
     * Log privilege escalation attempt
     */
    public function logPrivilegeEscalation($user_id, $attempted_action) {
        $this->logSecurityEvent('privilege_escalation', "Attempted unauthorized action: {$attempted_action}", $user_id);
    }
}

// Initialize security components
$rate_limiter = new RateLimiter($database);
$audit_logger = new AuditLogger($database);

// Security headers (already set in config.php, but can be enhanced here)
function setSecurityHeaders() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src \'self\' https://cdnjs.cloudflare.com; img-src \'self\' data:;');
}

// Call security headers
setSecurityHeaders();
?>
