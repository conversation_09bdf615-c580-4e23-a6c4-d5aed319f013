-- Sample data for Employee Leave Management System
USE employee_leave_system;

-- Insert sample departments
INSERT INTO departments (name, description) VALUES
('Computer Science', 'Computer Science and Information Technology Department'),
('Engineering', 'Engineering and Technical Studies Department'),
('Business Administration', 'Business and Management Department'),
('Human Resources', 'Human Resources and Personnel Department'),
('Finance', 'Finance and Accounting Department'),
('Academic Affairs', 'Academic Affairs and Student Services'),
('Research', 'Research and Development Department');

-- Insert admin user (password: admin123)
INSERT INTO users (name, email, password, role, department_id, address, phone, hire_date) VALUES
('System Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 4, 'Southern Technical University, Basra, Iraq', '+964-123-456-789', '2020-01-01');

-- Insert sample employees (password: password123 for all)
INSERT INTO users (name, email, password, role, department_id, address, phone, hire_date) VALUES
('<PERSON>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 1, 'Al-Ashar District, Basra, Iraq', '+964-************', '2021-03-15'),
('Fatima Mohammed Salim', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 2, 'Al-Jubail District, Basra, Iraq', '+964-************', '2021-06-01'),
('Omar Abdullah Karim', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 3, 'Al-Bradhiya District, Basra, Iraq', '+964-************', '2020-09-10'),
('Zainab Ali Hussein', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 1, 'Al-Qurna District, Basra, Iraq', '+964-************', '2022-01-20'),
('Hassan Mahmoud Jassim', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 5, 'Al-Zubair District, Basra, Iraq', '+964-************', '2021-11-05'),
('Maryam Saad Ibrahim', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', 6, 'Al-Hartha District, Basra, Iraq', '+964-776-678-9012', '2020-04-12');

-- Initialize leave balances for current year (2024)
-- Each employee gets 3 days per month
INSERT INTO leave_balances (user_id, month, year, days_earned, days_used, days_carried_forward, total_balance)
SELECT 
    u.id as user_id,
    m.month,
    2024 as year,
    3.00 as days_earned,
    0.00 as days_used,
    0.00 as days_carried_forward,
    3.00 as total_balance
FROM users u
CROSS JOIN (
    SELECT 1 as month UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION 
    SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION 
    SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12
) m
WHERE u.role = 'employee' AND u.hire_date <= CONCAT('2024-', LPAD(m.month, 2, '0'), '-01');

-- Sample leave requests
INSERT INTO leaves (user_id, from_date, to_date, days_requested, reason, substitute_name, substitute_address, status, approved_by, approved_at) VALUES
(2, '2024-01-15', '2024-01-17', 3, 'Family emergency - need to travel to Baghdad', 'Dr. Samir Al-Rashid', 'Computer Science Department, STU', 'approved', 1, '2024-01-10 10:30:00'),
(3, '2024-02-20', '2024-02-22', 3, 'Medical appointment and recovery', 'Eng. Layla Mahmoud', 'Engineering Department, STU', 'approved', 1, '2024-02-15 14:20:00'),
(4, '2024-03-10', '2024-03-12', 3, 'Wedding ceremony attendance', 'Dr. Nadia Fadhil', 'Business Administration Department, STU', 'pending', NULL, NULL),
(5, '2024-03-25', '2024-03-27', 3, 'Personal matters', 'Ms. Rana Khalil', 'Computer Science Department, STU', 'rejected', 1, '2024-03-20 09:15:00');

-- Update leave balances for used leaves
UPDATE leave_balances SET 
    days_used = 3.00,
    total_balance = days_earned + days_carried_forward - 3.00
WHERE user_id = 2 AND month = 1 AND year = 2024;

UPDATE leave_balances SET 
    days_used = 3.00,
    total_balance = days_earned + days_carried_forward - 3.00
WHERE user_id = 3 AND month = 2 AND year = 2024;
