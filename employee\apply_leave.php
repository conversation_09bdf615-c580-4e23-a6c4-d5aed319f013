<?php
require_once '../config/config.php';
requireAuth();

// Ensure user is employee
if (!isEmployee()) {
    header('Location: ../admin/dashboard.php');
    exit();
}

$user_id = getCurrentUserId();
$current_user = getCurrentUser();
$current_balance = calculateLeaveBalance($user_id);

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $data = [
            'from_date' => sanitize($_POST['from_date'] ?? ''),
            'to_date' => sanitize($_POST['to_date'] ?? ''),
            'reason' => sanitize($_POST['reason'] ?? ''),
            'substitute_name' => sanitize($_POST['substitute_name'] ?? ''),
            'substitute_address' => sanitize($_POST['substitute_address'] ?? '')
        ];
        
        // Validation
        if (empty($data['from_date']) || empty($data['to_date']) || empty($data['reason'])) {
            $error_message = 'Please fill in all required fields.';
        } elseif (!isValidDate($data['from_date']) || !isValidDate($data['to_date'])) {
            $error_message = 'Please provide valid dates.';
        } elseif (!isValidDateRange($data['from_date'], $data['to_date'])) {
            $error_message = 'End date must be after or equal to start date.';
        } elseif (strtotime($data['from_date']) < strtotime(date('Y-m-d'))) {
            $error_message = 'Leave start date cannot be in the past.';
        } else {
            // Calculate working days
            $days_requested = calculateWorkingDays($data['from_date'], $data['to_date']);
            
            if ($days_requested > $current_balance) {
                $error_message = "Insufficient leave balance. You have {$current_balance} days available, but requested {$days_requested} days.";
            } elseif ($days_requested > MAX_CONSECUTIVE_DAYS) {
                $error_message = "Maximum consecutive leave days allowed is " . MAX_CONSECUTIVE_DAYS . " days.";
            } else {
                // Check for overlapping leave requests
                $overlapping = $database->fetch(
                    "SELECT id FROM leaves 
                     WHERE user_id = ? 
                     AND status IN ('pending', 'approved')
                     AND (
                         (from_date <= ? AND to_date >= ?) OR
                         (from_date <= ? AND to_date >= ?) OR
                         (from_date >= ? AND to_date <= ?)
                     )",
                    [
                        $user_id,
                        $data['from_date'], $data['from_date'],
                        $data['to_date'], $data['to_date'],
                        $data['from_date'], $data['to_date']
                    ]
                );
                
                if ($overlapping) {
                    $error_message = 'You already have a leave request for overlapping dates.';
                } else {
                    // Insert leave request
                    try {
                        $database->execute(
                            "INSERT INTO leaves (user_id, from_date, to_date, days_requested, reason, substitute_name, substitute_address) 
                             VALUES (?, ?, ?, ?, ?, ?, ?)",
                            [
                                $user_id,
                                $data['from_date'],
                                $data['to_date'],
                                $days_requested,
                                $data['reason'],
                                $data['substitute_name'],
                                $data['substitute_address']
                            ]
                        );
                        
                        $leave_id = $database->lastInsertId();
                        
                        // Log activity
                        logActivity($user_id, 'leave_apply', "Applied for leave from {$data['from_date']} to {$data['to_date']}");
                        
                        $_SESSION['success_message'] = "Leave request submitted successfully! Your request ID is #{$leave_id}.";
                        header('Location: leave_history.php');
                        exit();
                        
                    } catch (Exception $e) {
                        $error_message = 'Error submitting leave request: ' . $e->getMessage();
                    }
                }
            }
        }
    }
}

$page_title = 'Apply for Leave';
include '../includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    Apply for Leave
                </h4>
            </div>
            <div class="card-body p-4">
                <!-- Current Balance Alert -->
                <div class="alert alert-info border-0 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle fa-lg me-3"></i>
                        <div>
                            <strong>Your Current Leave Balance: <?php echo number_format($current_balance, 1); ?> days</strong>
                            <br>
                            <small>You earn <?php echo MONTHLY_LEAVE_DAYS; ?> leave days per month. Unused days carry forward.</small>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="from_date" class="form-label">
                                <i class="fas fa-calendar me-2"></i>Leave From Date *
                            </label>
                            <input type="date" 
                                   class="form-control future-date" 
                                   id="from_date" 
                                   name="from_date" 
                                   value="<?php echo htmlspecialchars($data['from_date'] ?? ''); ?>"
                                   min="<?php echo date('Y-m-d'); ?>"
                                   required>
                            <div class="invalid-feedback">
                                Please select a valid start date.
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="to_date" class="form-label">
                                <i class="fas fa-calendar me-2"></i>Leave To Date *
                            </label>
                            <input type="date" 
                                   class="form-control" 
                                   id="to_date" 
                                   name="to_date" 
                                   value="<?php echo htmlspecialchars($data['to_date'] ?? ''); ?>"
                                   min="<?php echo date('Y-m-d'); ?>"
                                   required>
                            <div class="invalid-feedback">
                                Please select a valid end date.
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <label class="form-label mb-0">
                                <i class="fas fa-calculator me-2"></i>Working Days Requested
                            </label>
                            <span id="leave-days-display" class="badge bg-secondary">Select dates</span>
                        </div>
                        <small class="text-muted">Weekends are automatically excluded from the calculation.</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reason" class="form-label">
                            <i class="fas fa-comment me-2"></i>Reason for Leave *
                        </label>
                        <textarea class="form-control" 
                                  id="reason" 
                                  name="reason" 
                                  rows="4" 
                                  placeholder="Please provide a detailed reason for your leave request..."
                                  required><?php echo htmlspecialchars($data['reason'] ?? ''); ?></textarea>
                        <div class="invalid-feedback">
                            Please provide a reason for your leave.
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="substitute_name" class="form-label">
                                <i class="fas fa-user me-2"></i>Substitute Name
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="substitute_name" 
                                   name="substitute_name" 
                                   value="<?php echo htmlspecialchars($data['substitute_name'] ?? ''); ?>"
                                   placeholder="Name of person covering your duties">
                            <small class="text-muted">Optional: Person who will handle your responsibilities</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="substitute_address" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>Substitute Contact/Address
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="substitute_address" 
                                   name="substitute_address" 
                                   value="<?php echo htmlspecialchars($data['substitute_address'] ?? ''); ?>"
                                   placeholder="Contact information or department">
                            <small class="text-muted">Optional: How to reach the substitute</small>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning border-0 mb-4">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Important Notes:
                        </h6>
                        <ul class="mb-0 small">
                            <li>Leave requests must be submitted at least 24 hours in advance</li>
                            <li>Maximum consecutive leave days: <?php echo MAX_CONSECUTIVE_DAYS; ?> days</li>
                            <li>Approval is subject to department workload and availability</li>
                            <li>You will be notified via email once your request is processed</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="dashboard.php" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Submit Leave Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Leave Balance Breakdown -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Your Leave Balance Breakdown
                </h6>
            </div>
            <div class="card-body">
                <?php
                $current_year = date('Y');
                $current_month = date('n');
                
                // Get monthly balances for current year
                $monthly_balances = $database->fetchAll(
                    "SELECT month, days_earned, days_used, total_balance 
                     FROM leave_balances 
                     WHERE user_id = ? AND year = ? AND month <= ?
                     ORDER BY month",
                    [$user_id, $current_year, $current_month]
                );
                ?>
                
                <div class="row">
                    <?php if (!empty($monthly_balances)): ?>
                        <?php foreach ($monthly_balances as $balance): ?>
                            <div class="col-md-4 mb-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <div class="fw-bold text-primary">
                                        <?php echo date('M Y', mktime(0, 0, 0, $balance['month'], 1, $current_year)); ?>
                                    </div>
                                    <div class="small text-muted mt-1">
                                        Earned: <?php echo $balance['days_earned']; ?> | 
                                        Used: <?php echo $balance['days_used']; ?>
                                    </div>
                                    <div class="fw-semibold text-success">
                                        Balance: <?php echo number_format($balance['total_balance'], 1); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12 text-center text-muted">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <p>No balance data available for this year.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fromDate = document.getElementById('from_date');
    const toDate = document.getElementById('to_date');
    const daysDisplay = document.getElementById('leave-days-display');
    
    function calculateWorkingDays() {
        if (fromDate.value && toDate.value) {
            const start = new Date(fromDate.value);
            const end = new Date(toDate.value);
            
            if (end >= start) {
                let workingDays = 0;
                const current = new Date(start);
                
                while (current <= end) {
                    const dayOfWeek = current.getDay();
                    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
                        workingDays++;
                    }
                    current.setDate(current.getDate() + 1);
                }
                
                daysDisplay.textContent = workingDays + ' working day' + (workingDays !== 1 ? 's' : '');
                daysDisplay.className = 'badge bg-primary';
                
                // Check against available balance
                const currentBalance = <?php echo $current_balance; ?>;
                if (workingDays > currentBalance) {
                    daysDisplay.className = 'badge bg-danger';
                    daysDisplay.textContent += ' (Exceeds balance!)';
                }
            } else {
                daysDisplay.textContent = 'Invalid date range';
                daysDisplay.className = 'badge bg-danger';
            }
        } else {
            daysDisplay.textContent = 'Select dates';
            daysDisplay.className = 'badge bg-secondary';
        }
    }
    
    fromDate.addEventListener('change', function() {
        toDate.min = this.value;
        if (toDate.value && toDate.value < this.value) {
            toDate.value = this.value;
        }
        calculateWorkingDays();
    });
    
    toDate.addEventListener('change', function() {
        if (this.value < fromDate.value) {
            this.value = fromDate.value;
        }
        calculateWorkingDays();
    });
    
    // Initial calculation if dates are pre-filled
    calculateWorkingDays();
});
</script>

<?php include '../includes/footer.php'; ?>
