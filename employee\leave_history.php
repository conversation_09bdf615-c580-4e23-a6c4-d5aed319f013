<?php
require_once '../config/config.php';
requireAuth();

// Ensure user is employee
if (!isEmployee()) {
    header('Location: ../admin/dashboard.php');
    exit();
}

$user_id = getCurrentUserId();

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$year_filter = $_GET['year'] ?? date('Y');

// Build query conditions
$conditions = ["user_id = ?"];
$params = [$user_id];

if ($status_filter !== 'all') {
    $conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($year_filter !== 'all') {
    $conditions[] = "YEAR(from_date) = ?";
    $params[] = $year_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $conditions);

// Get leave requests with pagination
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

$total_query = "SELECT COUNT(*) as count FROM leaves {$where_clause}";
$total_result = $database->fetch($total_query, $params);
$total_records = $total_result ? $total_result['count'] : 0;
$total_pages = ceil($total_records / $per_page);

$leaves_query = "SELECT l.*, admin.name as approved_by_name
                 FROM leaves l 
                 LEFT JOIN users admin ON l.approved_by = admin.id
                 {$where_clause}
                 ORDER BY l.created_at DESC 
                 LIMIT {$per_page} OFFSET {$offset}";

$leaves = $database->fetchAll($leaves_query, $params);

// Get available years for filter
$years = $database->fetchAll(
    "SELECT DISTINCT YEAR(from_date) as year 
     FROM leaves 
     WHERE user_id = ? 
     ORDER BY year DESC",
    [$user_id]
);

$page_title = 'Leave History';
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-history me-2"></i>
                My Leave History
            </h1>
            <a href="apply_leave.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Apply for Leave
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Status</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                </select>
            </div>
            
            <div class="col-md-4">
                <label for="year" class="form-label">Year</label>
                <select name="year" id="year" class="form-select">
                    <option value="all" <?php echo $year_filter === 'all' ? 'selected' : ''; ?>>All Years</option>
                    <?php foreach ($years as $year): ?>
                        <option value="<?php echo $year['year']; ?>" 
                                <?php echo $year_filter == $year['year'] ? 'selected' : ''; ?>>
                            <?php echo $year['year']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Leave History -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            Leave Requests 
            <span class="badge bg-secondary"><?php echo $total_records; ?> total</span>
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($leaves)): ?>
            <div class="text-center py-5">
                <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No leave requests found</h5>
                <p class="text-muted mb-3">You haven't submitted any leave requests yet.</p>
                <a href="apply_leave.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Apply for Leave
                </a>
            </div>
        <?php else: ?>
            <!-- Desktop View -->
            <div class="table-responsive d-none d-md-block">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Request ID</th>
                            <th>Leave Period</th>
                            <th>Days</th>
                            <th>Reason</th>
                            <th>Status</th>
                            <th>Applied</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($leaves as $leave): ?>
                            <tr>
                                <td>
                                    <span class="fw-bold text-primary">#<?php echo $leave['id']; ?></span>
                                </td>
                                <td>
                                    <div><?php echo formatDate($leave['from_date']); ?></div>
                                    <small class="text-muted">to <?php echo formatDate($leave['to_date']); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $leave['days_requested']; ?> days</span>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" 
                                         data-bs-toggle="tooltip" 
                                         title="<?php echo htmlspecialchars($leave['reason']); ?>">
                                        <?php echo htmlspecialchars(substr($leave['reason'], 0, 50)); ?>
                                        <?php if (strlen($leave['reason']) > 50): ?>...<?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge status-<?php echo $leave['status']; ?>">
                                        <?php echo ucfirst($leave['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo formatDateTime($leave['created_at']); ?>
                                    </small>
                                </td>
                                <td>
                                    <a href="leave_details.php?id=<?php echo $leave['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Mobile View -->
            <div class="d-md-none">
                <?php foreach ($leaves as $leave): ?>
                    <div class="border-bottom p-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <span class="fw-bold text-primary">#<?php echo $leave['id']; ?></span>
                                <span class="badge status-<?php echo $leave['status']; ?> ms-2">
                                    <?php echo ucfirst($leave['status']); ?>
                                </span>
                            </div>
                            <a href="leave_details.php?id=<?php echo $leave['id']; ?>" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                        
                        <div class="mb-2">
                            <strong><?php echo formatDate($leave['from_date']); ?></strong>
                            <span class="text-muted">to</span>
                            <strong><?php echo formatDate($leave['to_date']); ?></strong>
                            <span class="badge bg-info ms-2"><?php echo $leave['days_requested']; ?> days</span>
                        </div>
                        
                        <div class="text-muted small mb-2">
                            <?php echo htmlspecialchars(substr($leave['reason'], 0, 100)); ?>
                            <?php if (strlen($leave['reason']) > 100): ?>...<?php endif; ?>
                        </div>
                        
                        <small class="text-muted">
                            Applied: <?php echo formatDateTime($leave['created_at']); ?>
                        </small>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
<nav aria-label="Leave history pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&year=<?php echo $year_filter; ?>">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        <?php endif; ?>
        
        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&year=<?php echo $year_filter; ?>">
                    <?php echo $i; ?>
                </a>
            </li>
        <?php endfor; ?>
        
        <?php if ($page < $total_pages): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&year=<?php echo $year_filter; ?>">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        <?php endif; ?>
    </ul>
</nav>
<?php endif; ?>

<!-- Summary Card -->
<div class="card border-0 shadow-sm mt-4">
    <div class="card-header bg-light">
        <h6 class="mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            Summary for <?php echo $year_filter === 'all' ? 'All Years' : $year_filter; ?>
        </h6>
    </div>
    <div class="card-body">
        <?php
        // Calculate summary statistics
        $summary_stats = [];
        
        $summary_conditions = ["user_id = ?"];
        $summary_params = [$user_id];
        
        if ($year_filter !== 'all') {
            $summary_conditions[] = "YEAR(from_date) = ?";
            $summary_params[] = $year_filter;
        }
        
        $summary_where = 'WHERE ' . implode(' AND ', $summary_conditions);
        
        // Total requests
        $summary_stats['total'] = $database->fetch(
            "SELECT COUNT(*) as count FROM leaves {$summary_where}",
            $summary_params
        )['count'];
        
        // Approved requests
        $summary_stats['approved'] = $database->fetch(
            "SELECT COUNT(*) as count FROM leaves {$summary_where} AND status = 'approved'",
            $summary_params
        )['count'];
        
        // Pending requests
        $summary_stats['pending'] = $database->fetch(
            "SELECT COUNT(*) as count FROM leaves {$summary_where} AND status = 'pending'",
            $summary_params
        )['count'];
        
        // Rejected requests
        $summary_stats['rejected'] = $database->fetch(
            "SELECT COUNT(*) as count FROM leaves {$summary_where} AND status = 'rejected'",
            $summary_params
        )['count'];
        
        // Total days used
        $summary_stats['days_used'] = $database->fetch(
            "SELECT COALESCE(SUM(days_requested), 0) as total FROM leaves {$summary_where} AND status = 'approved'",
            $summary_params
        )['total'];
        ?>
        
        <div class="row text-center">
            <div class="col-6 col-md-2">
                <div class="fw-bold text-primary h4"><?php echo $summary_stats['total']; ?></div>
                <small class="text-muted">Total Requests</small>
            </div>
            <div class="col-6 col-md-2">
                <div class="fw-bold text-success h4"><?php echo $summary_stats['approved']; ?></div>
                <small class="text-muted">Approved</small>
            </div>
            <div class="col-6 col-md-2">
                <div class="fw-bold text-warning h4"><?php echo $summary_stats['pending']; ?></div>
                <small class="text-muted">Pending</small>
            </div>
            <div class="col-6 col-md-2">
                <div class="fw-bold text-danger h4"><?php echo $summary_stats['rejected']; ?></div>
                <small class="text-muted">Rejected</small>
            </div>
            <div class="col-12 col-md-4">
                <div class="fw-bold text-info h4"><?php echo $summary_stats['days_used']; ?> days</div>
                <small class="text-muted">Total Days Used</small>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
